# 用户行为分析系统 - Docker部署

本项目提供了完整的Docker容器化解决方案，可以一键部署Django Web服务和调度器服务。

## 📋 系统要求

- Docker 20.10+
- 至少2GB可用内存
- 至少5GB可用磁盘空间

## 🚀 快速开始

### 方法一：一键部署脚本（最简单）

```bash
# Linux/macOS
chmod +x deploy.sh
./deploy.sh

# Windows
deploy.bat

# 自定义端口
./deploy.sh --port 9997
```

### 方法二：使用Python部署脚本

```bash
# 构建并运行容器
python docker_deploy.py build-run

# 查看服务状态
python docker_deploy.py status

# 查看日志
python docker_deploy.py logs
```

### 方法三：手动Docker命令

```bash
# 构建镜像
docker build -t userbehavior-app:latest .

# 创建必要目录
mkdir -p conf logs data

# 复制配置文件模板
cp config.example.yml conf/config.yml

# 运行容器
docker run -d \
  --name userbehavior-app \
  -p 8035:8035 \
  -e CONFIG_FILE_PATH=/app/conf/config.yml \
  -v $(pwd)/conf:/app/conf \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/data:/app/data \
  userbehavior-app:latest
```

## ⚙️ 配置管理

系统使用外部映射的 `config.yml` 文件进行配置管理，支持以下配置：

- **数据库配置**: MySQL连接信息
- **Elasticsearch配置**: ES集群连接信息
- **Redis配置**: 缓存服务配置
- **调度器配置**: 定时任务执行时间
- **应用配置**: Django基础设置

### 配置文件管理

容器启动时会从映射的 `/app/conf/config.yml` 读取配置，实现配置与镜像分离：

```bash
# 目录结构
./
├── conf/
│   └── config.yml          # 配置文件
├── logs/                   # 日志目录（自动创建）
└── data/                   # 数据目录（自动创建）

# 修改配置
vim conf/config.yml

# 重启容器使配置生效
docker restart userbehavior-app
```

### 多环境部署

同一个镜像可以在不同环境运行，只需要使用不同的配置文件：

```bash
# 开发环境
docker run -d \
  --name userbehavior-dev \
  -p 8035:8035 \
  -e CONFIG_FILE_PATH=/app/conf/config.yml \
  -v $(pwd)/conf/dev/config.yml:/app/conf/config.yml \
  -v $(pwd)/logs/dev:/app/logs \
  userbehavior-app:latest

# 生产环境
docker run -d \
  --name userbehavior-prod \
  -p 9997:8035 \
  -e CONFIG_FILE_PATH=/app/conf/config.yml \
  -v $(pwd)/conf/prod/config.yml:/app/conf/config.yml \
  -v $(pwd)/logs/prod:/app/logs \
  userbehavior-app:latest
```

## 📋 服务说明

容器启动后会自动运行两个服务：

1. **Django Web服务** (端口8035)
   - 提供REST API接口
   - 用户认证和权限管理
   - 数据分析和报表功能

2. **调度器服务** (后台运行)
   - 每天凌晨1:00自动执行数据处理任务
   - 处理所有系统类型的数据 (api, oss, boss)
   - 独立的日志记录

## 🔧 部署脚本使用

`docker_deploy.py` 脚本提供了完整的Docker管理功能：

```bash
# 构建镜像
python docker_deploy.py build

# 运行容器
python docker_deploy.py run

# 构建并运行
python docker_deploy.py build-run

# 查看状态和日志
python docker_deploy.py status
python docker_deploy.py logs

# 清理资源
python docker_deploy.py cleanup

# 自定义参数
python docker_deploy.py build-run \
  --name userbehavior-prod \
  --port 9997 \
  --conf-dir ./conf/prod \
  --logs-dir ./logs/prod
```

## 🌐 访问地址

服务启动后可通过以下地址访问：

- **API基础地址**: http://localhost:8035/api/
- **健康检查**: http://localhost:8035/api/analytics/health/
- **用户登录**: http://localhost:8035/api/analytics/users/auth/login/
- **操作日志**: http://localhost:8035/api/analytics/operation-logs/
- **访问统计**: http://localhost:8035/api/analytics/access-stats/

## 📊 默认用户

系统提供默认管理员账户：

- **用户名**: admin
- **密码**: tgt51848

## 📝 日志管理

### 日志文件位置

映射到宿主机的日志文件：

- Django Web服务: `logs/django-web.log`
- Django错误日志: `logs/django-error.log`
- 调度器服务: `logs/scheduler/scheduler-service.log`
- Docker启动日志: `logs/docker_start.log`

### 查看日志命令

```bash
# 查看容器日志
docker logs -f userbehavior-app

# 查看映射的日志文件
tail -f logs/django-web.log
tail -f logs/scheduler/scheduler-service.log

# 使用部署脚本查看日志
python docker_deploy.py logs
```

## 🔍 健康检查

容器配置了自动健康检查：

```bash
# 检查容器健康状态
docker ps

# 手动健康检查
curl http://localhost:8035/api/analytics/health/
```

## ⚙️ 配置说明

### 环境变量

- `PYTHONUNBUFFERED=1`: 禁用Python输出缓冲
- `TZ=Asia/Shanghai`: 设置时区为北京时间

### 数据卷挂载

- `./logs:/app/logs`: 日志文件持久化
- `./data:/app/data`: 数据文件持久化

### 端口映射

- `8035:8035`: Django Web服务端口

## 🛠️ 故障排除

### 1. 容器启动失败

```bash
# 查看详细日志
docker logs userbehavior-app

# 进入容器调试
docker exec -it userbehavior-app /bin/bash
```

### 2. 服务无法访问

```bash
# 检查端口映射
docker port userbehavior-app

# 检查防火墙设置
curl http://localhost:8035/api/analytics/health/
```

### 3. 数据库连接问题

确保外部MySQL服务可访问，检查 `src/config/settings.py` 中的数据库配置。

### 4. Elasticsearch连接问题

确保外部ES服务可访问，检查 `src/config/settings.py` 中的ES配置。

## 🔄 更新部署

```bash
# 方法一：重新构建镜像
docker stop userbehavior-app
docker rm userbehavior-app
python docker_deploy.py build-run

# 方法二：仅重启容器（配置文件修改后）
docker restart userbehavior-app

# 方法三：使用新镜像
docker pull userbehavior-app:latest
docker stop userbehavior-app
docker rm userbehavior-app
python docker_deploy.py run
```

## 🧹 清理资源

```bash
# 使用部署脚本清理
python docker_deploy.py cleanup

# 手动清理
docker stop userbehavior-app
docker rm userbehavior-app
docker rmi userbehavior-app:latest
docker system prune -f
```

## 📚 相关文档

- [完整Docker部署指南](docs/docker_deployment_guide.md)
- [配置文件说明](docs/configuration_guide.md)
- [配置文件路径指定示例](docs/config_path_examples.md)
- [PyYAML使用指南](docs/pyyaml_usage_guide.md)
- [API接口文档](docs/api/)
- [系统部署指南](docs/deployment_guide.md)

## 🆘 技术支持

如果遇到问题，请检查：

1. Docker和Docker Compose版本是否符合要求
2. 端口8035是否被其他服务占用
3. 外部依赖服务（MySQL、Redis、Elasticsearch）是否可访问
4. 日志文件中的错误信息

更多详细信息请参考 [Docker部署指南](docs/docker_deployment_guide.md)。
