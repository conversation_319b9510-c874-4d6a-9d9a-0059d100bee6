FROM registry.cn-shanghai.aliyuncs.com/tgtdev/common_images:basepython-3.8

# 复制requirements.txt并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目文件
COPY . .

# 创建必要的目录
RUN mkdir -p conf logs/scheduler logs/task data static

# 设置文件权限
RUN chmod +x run_server.py run_server_with_config.py simple_scheduler.py start_all.py docker_start.py

# 暴露端口
EXPOSE 8035

# 启动命令
CMD ["python", "docker_start.py"]
