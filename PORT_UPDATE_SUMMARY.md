# 端口更新总结

## 🔄 端口变更

**原端口**: 8000  
**新端口**: 8035

## 📝 更新的文件

### 1. 核心应用文件
- **Dockerfile** - 更新EXPOSE端口和健康检查URL
- **run_server.py** - 更新Django服务器绑定端口
- **run_server_with_config.py** - 更新默认端口参数
- **docker_start.py** - 容器启动脚本（无需修改，使用run_server.py）

### 2. 部署脚本
- **docker_deploy.py** - 更新默认端口和端口映射
- **deploy.sh** - 更新默认端口和Docker运行命令
- **deploy.bat** - 更新默认端口和Docker运行命令

### 3. 文档文件
- **README.md** - 更新所有示例中的端口
- **Docker_README.md** - 更新所有端口引用
- **DEPLOYMENT.md** - 更新部署示例中的端口
- **docs/config_path_examples.md** - 更新配置示例中的端口

## 🚀 部署命令更新

### 快速部署
```bash
# 一键部署（新端口8035）
./deploy.sh

# 自定义端口
./deploy.sh --port 9997
```

### 手动部署
```bash
# 构建镜像
docker build -t userbehavior-app:latest .

# 运行容器（新端口映射）
docker run -d \
  --name userbehavior-app \
  -p 8035:8035 \
  -e CONFIG_FILE_PATH=/app/conf/config.yml \
  -v $(pwd)/conf:/app/conf \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/data:/app/data \
  userbehavior-app:latest
```

### Python部署脚本
```bash
# 使用新的默认端口8035
python docker_deploy.py build-run

# 自定义端口
python docker_deploy.py build-run --port 9997
```

## 🌐 访问地址更新

### 新的访问地址
- **服务地址**: http://localhost:8035
- **API基础地址**: http://localhost:8035/api/
- **健康检查**: http://localhost:8035/api/analytics/health/
- **用户登录**: http://localhost:8035/api/analytics/users/auth/login/
- **操作日志**: http://localhost:8035/api/analytics/operation-logs/
- **访问统计**: http://localhost:8035/api/analytics/access-stats/

### 多环境部署示例
```bash
# 开发环境（使用新默认端口）
python docker_deploy.py build-run \
  --name userbehavior-dev \
  --port 8035 \
  --conf-dir ./conf/dev

# 生产环境（使用自定义端口）
python docker_deploy.py build-run \
  --name userbehavior-prod \
  --port 9997 \
  --conf-dir ./conf/prod
```

## 🔧 配置文件

配置文件本身无需修改，端口变更只影响：
1. Django服务器启动端口
2. Docker容器暴露端口
3. 端口映射配置
4. 健康检查URL

## ✅ 验证部署

部署完成后，使用以下命令验证：

```bash
# 检查容器状态
docker ps

# 检查服务健康（新端口）
curl http://localhost:8035/api/analytics/health/

# 查看容器日志
docker logs userbehavior-app
```

## 📋 注意事项

1. **防火墙设置**: 如果使用防火墙，需要开放8035端口
2. **反向代理**: 如果使用Nginx等反向代理，需要更新配置
3. **监控系统**: 需要更新监控系统中的端口配置
4. **文档同步**: 所有相关文档已同步更新

## 🔄 回滚方案

如需回滚到8000端口，需要修改以下文件中的端口配置：
- Dockerfile (EXPOSE和HEALTHCHECK)
- run_server.py (runserver端口)
- 所有部署脚本的默认端口
- 重新构建Docker镜像

现在系统默认使用8035端口，所有部署脚本和文档都已更新！
