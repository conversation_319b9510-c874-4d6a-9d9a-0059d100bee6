# 用户行为分析系统

基于Django和Elasticsearch的用户行为分析系统，提供用户操作日志分析、访问统计、数据可视化等功能。

## 🚀 快速部署

### Docker部署（推荐）

```bash
# 1. 拉取代码
git clone <repository-url>
cd userbehavior

# 2. 一键部署
./deploy.sh

# 3. 访问服务
# http://localhost:8035
```

### 手动部署

```bash
# 1. 构建镜像
docker build -t userbehavior-app:latest .

# 2. 运行容器
docker run -d \
  --name userbehavior-app \
  -p 8035:8035 \
  -e CONFIG_FILE_PATH=/app/conf/config.yml \
  -v $(pwd)/conf:/app/conf \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/data:/app/data \
  userbehavior-app:latest
```

## 📋 功能特性

- **用户操作日志分析** - 收集和分析用户行为数据
- **实时访问统计** - 提供实时的访问统计和报表
- **多维度数据分析** - 支持多种维度的数据分析
- **RESTful API** - 完整的REST API接口
- **定时任务** - 自动化数据处理和分析
- **用户认证** - 完整的用户认证和权限管理
- **配置管理** - 支持外部配置文件管理

## 🛠️ 技术栈

- **后端框架**: Django 3.2
- **数据库**: MySQL 5.7
- **搜索引擎**: Elasticsearch 7.17
- **缓存**: Redis
- **任务调度**: APScheduler
- **API框架**: Django REST Framework
- **容器化**: Docker

## 📁 项目结构

```
./
├── src/                    # 源代码目录
│   ├── apps/              # Django应用
│   ├── config/            # 配置文件
│   └── utils/             # 工具类
├── conf/                  # 外部配置目录
│   └── config.yml         # 配置文件
├── logs/                  # 日志目录
├── data/                  # 数据目录
├── docs/                  # 文档目录
├── Dockerfile             # Docker构建文件
├── deploy.sh              # 一键部署脚本
└── requirements.txt       # Python依赖
```

## ⚙️ 配置管理

系统使用YAML配置文件进行管理，支持：

- 数据库配置
- Elasticsearch配置
- Redis配置
- 调度器配置
- 应用配置

配置文件位置：`conf/config.yml`

## 🌐 API接口

- **健康检查**: `GET /api/analytics/health/`
- **用户登录**: `POST /api/analytics/users/auth/login/`
- **操作日志**: `GET /api/analytics/operation-logs/`
- **访问统计**: `GET /api/analytics/access-stats/`

详细API文档：[API接口文档](docs/api/)

## 📊 默认用户

- **用户名**: admin
- **密码**: tgt51848

## 📚 文档

- [Docker部署指南](Docker_README.md)
- [完整部署指南](DEPLOYMENT.md)
- [配置文件说明](docs/configuration_guide.md)
- [API接口文档](docs/api/)

## 🔧 开发环境

如需本地开发，请参考：

```bash
# 安装依赖
pip install -r requirements.txt

# 配置环境
export CONFIG_FILE_PATH=./config.yml

# 启动服务
python run_server.py
python simple_scheduler.py
```

## 📝 许可证

MIT License