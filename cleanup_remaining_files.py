#!/usr/bin/env python
# 清理剩余测试文件脚本

import os
import glob

# 要保留的文件列表
files_to_keep = [
    'run_server.py',
    'simple_scheduler_new.py',
    'start_all.py',
    'run_task_with_all_systems.py',
    'django_mysql_patch.py',
    'setup.py',
    'cleanup_remaining_files.py'  # 保留当前脚本
]

# 获取当前目录下的所有Python文件
all_python_files = glob.glob('*.py')

# 计算要删除的文件
files_to_remove = [f for f in all_python_files if f not in files_to_keep]

# 打印要删除的文件列表
print(f"将要删除以下 {len(files_to_remove)} 个文件:")
for file in files_to_remove:
    print(f"  - {file}")

# 确认删除
confirmation = input("\n确认删除这些文件? (y/n): ")
if confirmation.lower() == 'y':
    # 删除文件
    for file in files_to_remove:
        try:
            os.remove(file)
            print(f"已删除: {file}")
        except Exception as e:
            print(f"删除 {file} 时出错: {str(e)}")
    print("\n清理完成!")
else:
    print("\n操作已取消，没有文件被删除。")
