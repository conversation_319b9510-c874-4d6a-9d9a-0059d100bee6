#!/usr/bin/env python
# 创建管理员用户脚本

import os
import sys
import django
from django.contrib.auth.hashers import make_password

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'src.config.settings')
django.setup()

# 导入Django User模型
from django.contrib.auth import get_user_model

User = get_user_model()

def create_admin_user():
    """创建管理员用户"""
    # 检查admin用户是否已存在
    if User.objects.filter(username='admin').exists():
        # 删除现有admin用户
        User.objects.filter(username='admin').delete()
        print("已删除现有管理员用户")

    # 创建admin用户
    admin_user = User.objects.create_user(
        username='admin',
        password='tgt51848',  # 明文密码，会自动哈希
        email='<EMAIL>',
        is_active=True,
        is_staff=True,
        is_superuser=True
    )

    print(f"管理员用户创建成功: {admin_user.username}")

if __name__ == "__main__":
    create_admin_user()
