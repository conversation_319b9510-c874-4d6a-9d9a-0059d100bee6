#!/bin/bash
# 用户行为分析系统 - 一键部署脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 默认参数
CONTAINER_NAME="userbehavior-app"
IMAGE_TAG="userbehavior-app:latest"
PORT=8035
CONF_DIR="./conf"
LOGS_DIR="./logs"
DATA_DIR="./data"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --name)
            CONTAINER_NAME="$2"
            shift 2
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --conf-dir)
            CONF_DIR="$2"
            shift 2
            ;;
        --logs-dir)
            LOGS_DIR="$2"
            shift 2
            ;;
        --data-dir)
            DATA_DIR="$2"
            shift 2
            ;;
        --help|-h)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --name CONTAINER_NAME    容器名称 (默认: userbehavior-app)"
            echo "  --port PORT              端口映射 (默认: 8035)"
            echo "  --conf-dir DIR           配置文件目录 (默认: ./conf)"
            echo "  --logs-dir DIR           日志文件目录 (默认: ./logs)"
            echo "  --data-dir DIR           数据文件目录 (默认: ./data)"
            echo "  --help, -h               显示帮助信息"
            exit 0
            ;;
        *)
            print_error "未知参数: $1"
            exit 1
            ;;
    esac
done

print_info "开始部署用户行为分析系统..."
print_info "容器名称: $CONTAINER_NAME"
print_info "端口映射: $PORT"
print_info "配置目录: $CONF_DIR"
print_info "日志目录: $LOGS_DIR"
print_info "数据目录: $DATA_DIR"

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    print_error "Docker未安装，请先安装Docker"
    exit 1
fi

# 检查Docker是否运行
if ! docker info &> /dev/null; then
    print_error "Docker未运行，请启动Docker服务"
    exit 1
fi

print_success "Docker环境检查通过"

# 创建必要的目录
print_info "创建必要的目录..."
mkdir -p "$CONF_DIR" "$LOGS_DIR" "$DATA_DIR"

# 检查配置文件
CONFIG_FILE="$CONF_DIR/config.yml"
if [ ! -f "$CONFIG_FILE" ]; then
    print_warning "配置文件不存在: $CONFIG_FILE"
    if [ -f "config.example.yml" ]; then
        print_info "复制示例配置文件..."
        cp config.example.yml "$CONFIG_FILE"
        print_success "已复制示例配置到: $CONFIG_FILE"
        print_warning "请根据实际环境修改配置文件"
    else
        print_warning "将使用容器内默认配置"
    fi
else
    print_success "配置文件已存在: $CONFIG_FILE"
fi

# 停止并删除现有容器
print_info "检查现有容器..."
if docker ps -a --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
    print_info "停止现有容器: $CONTAINER_NAME"
    docker stop "$CONTAINER_NAME" || true
    print_info "删除现有容器: $CONTAINER_NAME"
    docker rm "$CONTAINER_NAME" || true
fi

# 构建Docker镜像
print_info "构建Docker镜像: $IMAGE_TAG"
if docker build -t "$IMAGE_TAG" .; then
    print_success "镜像构建成功"
else
    print_error "镜像构建失败"
    exit 1
fi

# 运行容器
print_info "启动容器: $CONTAINER_NAME"
DOCKER_CMD="docker run -d \
    --name $CONTAINER_NAME \
    -p $PORT:8035 \
    -e CONFIG_FILE_PATH=/app/conf/config.yml \
    -v $(pwd)/$CONF_DIR:/app/conf \
    -v $(pwd)/$LOGS_DIR:/app/logs \
    -v $(pwd)/$DATA_DIR:/app/data \
    $IMAGE_TAG"

if eval "$DOCKER_CMD"; then
    print_success "容器启动成功"
else
    print_error "容器启动失败"
    exit 1
fi

# 等待服务启动
print_info "等待服务启动..."
sleep 10

# 检查容器状态
if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "$CONTAINER_NAME.*Up"; then
    print_success "容器运行正常"
else
    print_error "容器启动异常"
    print_info "查看容器日志:"
    docker logs "$CONTAINER_NAME"
    exit 1
fi

# 健康检查
print_info "执行健康检查..."
if curl -f "http://localhost:$PORT/api/analytics/health/" &> /dev/null; then
    print_success "健康检查通过"
else
    print_warning "健康检查失败，服务可能还在启动中"
fi

# 显示部署信息
echo ""
echo "=========================================="
print_success "部署完成！"
echo "=========================================="
echo "🌐 服务访问地址: http://localhost:$PORT"
echo "📋 API文档: http://localhost:$PORT/api/"
echo "🔍 健康检查: http://localhost:$PORT/api/analytics/health/"
echo ""
echo "📁 映射目录:"
echo "  配置文件: $(pwd)/$CONF_DIR"
echo "  日志文件: $(pwd)/$LOGS_DIR"
echo "  数据文件: $(pwd)/$DATA_DIR"
echo ""
echo "📝 常用命令:"
echo "  查看容器状态: docker ps"
echo "  查看容器日志: docker logs -f $CONTAINER_NAME"
echo "  停止容器: docker stop $CONTAINER_NAME"
echo "  重启容器: docker restart $CONTAINER_NAME"
echo ""
echo "⚙️ 配置文件: $CONFIG_FILE"
echo "  修改配置后执行: docker restart $CONTAINER_NAME"
echo "=========================================="
