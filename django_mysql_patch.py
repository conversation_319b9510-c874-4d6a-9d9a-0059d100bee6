# Django MySQL兼容性补丁

"""
这个补丁解决了Django 2.2.28在Python 3.11中使用PyMySQL连接MySQL 5.7时的兼容性问题
主要修复了'str' object has no attribute 'decode'错误
"""

import sys
import django
from django.db.backends.mysql.operations import DatabaseOperations

# 保存原始的last_executed_query方法
original_last_executed_query = DatabaseOperations.last_executed_query

# 创建修复的last_executed_query方法
def patched_last_executed_query(self, cursor, sql, params):
    # 检查sql是否已经是字符串类型
    if isinstance(sql, str):
        # 如果已经是字符串，不需要decode
        return sql
    else:
        # 如果不是字符串，使用原始方法
        return original_last_executed_query(self, cursor, sql, params)

# 应用补丁
DatabaseOperations.last_executed_query = patched_last_executed_query

print("已应用Django MySQL兼容性补丁")