#!/usr/bin/env python
# Docker部署脚本

import os
import sys
import subprocess
import argparse
from datetime import datetime

def run_command(cmd, description=""):
    """执行命令并处理错误"""
    print(f"\n{'='*50}")
    if description:
        print(f"执行: {description}")
    print(f"命令: {cmd}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            print("输出:")
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: 命令执行失败")
        print(f"返回码: {e.returncode}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False

def build_image(tag="userbehavior-app:latest"):
    """构建Docker镜像"""
    print(f"开始构建Docker镜像: {tag}")
    
    # 检查Dockerfile是否存在
    if not os.path.exists("Dockerfile"):
        print("错误: 未找到Dockerfile文件")
        return False
    
    # 构建镜像
    cmd = f"docker build -t {tag} ."
    return run_command(cmd, "构建Docker镜像")

def run_container(tag="userbehavior-app:latest", container_name="userbehavior-app", port=8035,
                 conf_dir="./conf", logs_dir="./logs", data_dir="./data"):
    """运行Docker容器"""
    print(f"开始运行Docker容器: {container_name}")
    
    # 停止并删除现有容器（如果存在）
    print("检查并清理现有容器...")
    run_command(f"docker stop {container_name}", "停止现有容器")
    run_command(f"docker rm {container_name}", "删除现有容器")
    
    # 创建必要的目录
    os.makedirs(conf_dir, exist_ok=True)
    os.makedirs(logs_dir, exist_ok=True)
    os.makedirs(data_dir, exist_ok=True)
    
    # 检查配置文件是否存在
    config_file = os.path.join(conf_dir, "config.yml")
    if not os.path.exists(config_file):
        print(f"警告: 配置文件不存在 {config_file}")
        if os.path.exists("config.example.yml"):
            print("复制示例配置文件...")
            import shutil
            shutil.copy("config.example.yml", config_file)
            print(f"已复制示例配置到: {config_file}")
        else:
            print("将使用容器内默认配置")
    
    # 运行新容器
    cmd = f"""docker run -d \
        --name {container_name} \
        -p {port}:8035 \
        -e CONFIG_FILE_PATH=/app/conf/config.yml \
        -v {os.path.abspath(conf_dir)}:/app/conf \
        -v {os.path.abspath(logs_dir)}:/app/logs \
        -v {os.path.abspath(data_dir)}:/app/data \
        {tag}"""
    
    return run_command(cmd, "运行Docker容器")

def show_status():
    """显示容器状态"""
    print("\n" + "="*50)
    print("Docker容器状态")
    print("="*50)
    
    run_command("docker ps", "查看运行中的容器")

def show_logs(container_name="userbehavior-app"):
    """显示容器日志"""
    print(f"\n显示容器 {container_name} 的日志:")
    print("="*50)
    
    # 显示最近的日志
    run_command(f"docker logs --tail 50 {container_name}", "查看容器日志")

def cleanup(container_name="userbehavior-app", image_tag="userbehavior-app:latest"):
    """清理Docker资源"""
    print("\n开始清理Docker资源...")
    
    # 停止并删除容器
    run_command(f"docker stop {container_name}", "停止容器")
    run_command(f"docker rm {container_name}", "删除容器")
    
    # 删除镜像
    run_command(f"docker rmi {image_tag}", "删除镜像")

def main():
    parser = argparse.ArgumentParser(description="Docker部署脚本")
    parser.add_argument("action", choices=[
        "build", "run", "build-run", "status", "logs", "cleanup"
    ], help="要执行的操作")
    parser.add_argument("--tag", default="userbehavior-app:latest", help="Docker镜像标签")
    parser.add_argument("--name", default="userbehavior-app", help="容器名称")
    parser.add_argument("--port", type=int, default=8035, help="端口映射")
    parser.add_argument("--conf-dir", default="./conf", help="配置文件目录")
    parser.add_argument("--logs-dir", default="./logs", help="日志文件目录")
    parser.add_argument("--data-dir", default="./data", help="数据文件目录")
    
    args = parser.parse_args()
    
    print(f"Docker部署脚本 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"执行操作: {args.action}")
    
    success = True
    
    if args.action == "build":
        success = build_image(args.tag)
    elif args.action == "run":
        success = run_container(args.tag, args.name, args.port, 
                               args.conf_dir, args.logs_dir, args.data_dir)
    elif args.action == "build-run":
        success = build_image(args.tag)
        if success:
            success = run_container(args.tag, args.name, args.port,
                                   args.conf_dir, args.logs_dir, args.data_dir)
    elif args.action == "status":
        show_status()
    elif args.action == "logs":
        show_logs(args.name)
    elif args.action == "cleanup":
        cleanup(args.name, args.tag)
    
    if success:
        print(f"\n✅ 操作 '{args.action}' 执行成功!")
        
        if args.action in ["run", "build-run"]:
            print(f"\n🌐 服务访问地址: http://localhost:{args.port}")
            print("📋 API文档: http://localhost:8035/api/")
            print("🔍 健康检查: http://localhost:8035/api/analytics/health/")
            
            print(f"\n📁 映射目录:")
            print(f"  配置文件: {os.path.abspath(args.conf_dir)}")
            print(f"  日志文件: {os.path.abspath(args.logs_dir)}")
            print(f"  数据文件: {os.path.abspath(args.data_dir)}")
            
            print(f"\n📝 查看日志命令:")
            print(f"  docker logs -f {args.name}")
            print(f"  python docker_deploy.py logs --name {args.name}")
            
            print(f"\n🛑 停止服务命令:")
            print(f"  docker stop {args.name}")
            
            print(f"\n⚙️ 配置文件: {args.conf_dir}/config.yml")
            print("  修改配置文件后重启容器即可生效")
    else:
        print(f"\n❌ 操作 '{args.action}' 执行失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
