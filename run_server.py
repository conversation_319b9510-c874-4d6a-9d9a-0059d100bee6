# 启动Django服务器脚本

import os
import sys

# 应用兼容性补丁

# 1. 直接使用collections.abc模块，避免过时警告
import collections.abc
# 不再使用collections.Mapping补丁
print("collections.abc模块已导入")

# 2. 确保PyMySQL已安装
try:
    import pymysql
    pymysql.version_info = (1, 4, 6, 'final', 0)  # 伪装成MySQLdb
    pymysql.install_as_MySQLdb()
    print("PyMySQL已安装为MySQLdb")
except ImportError:
    print("错误：未找到PyMySQL包，请安装：pip install PyMySQL==1.0.2")
    sys.exit(1)

# 3. 导入Django MySQL兼容性补丁
try:
    import django_mysql_patch
    print("Django MySQL补丁已应用")
except ImportError:
    print("警告：未找到Django MySQL兼容性补丁文件，如果遇到str.decode错误，请确保django_mysql_patch.py存在")

# 不再导入APScheduler相关补丁，因为我们使用单独的简单调度器

# 导入日志配置
from src.config.logging_config import configure_logging

# 使用web类型的日志配置
configure_logging(service_type='web')

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'src.config.settings')

# 导入Django相关模块
try:
    import django
    django.setup()
    from django.core.management import call_command
    print("Django环境已成功设置")

    # 初始化ES客户端
    try:
        from src.utils.es_client import es_client
        print(f"Elasticsearch客户端已初始化，连接到: {es_client.transport.hosts}")
    except ImportError as e:
        print(f"警告：导入ES客户端失败: {str(e)}")

except ImportError as e:
    print(f"导入Django模块失败: {str(e)}")
    print("请确保已安装Django并且项目结构正确")
    sys.exit(1)

# 启动服务器
if __name__ == "__main__":
    print("启动Django服务器...")
    print(f"当前工作目录: {os.getcwd()}")

    # 运行Django开发服务器 - 绑定到所有接口，使其可从任何IP访问
    call_command('runserver', '0.0.0.0:8035')