#!/usr/bin/env python
# 支持指定配置文件的Django服务器启动脚本

import os
import sys
import argparse
from pathlib import Path

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='启动Django服务器')
    parser.add_argument(
        '--config', '-c',
        dest='config_file',
        help='配置文件路径，例如: --config /data/conf/config.yml'
    )
    parser.add_argument(
        '--host',
        default='0.0.0.0',
        help='服务器绑定地址，默认: 0.0.0.0'
    )
    parser.add_argument(
        '--port', '-p',
        default='8035',
        help='服务器端口，默认: 8035'
    )
    parser.add_argument(
        '--spring-config-location',
        dest='spring_config',
        help='Spring Boot风格的配置文件路径，例如: --spring-config-location=/data/conf/config.yml'
    )
    
    return parser.parse_args()

def set_config_environment(args):
    """设置配置文件环境变量"""
    if args.config_file:
        os.environ['CONFIG_FILE_PATH'] = args.config_file
        print(f"使用配置文件: {args.config_file}")
    elif args.spring_config:
        os.environ['SPRING_CONFIG_LOCATION'] = args.spring_config
        print(f"使用Spring Boot风格配置文件: {args.spring_config}")
    else:
        print("使用默认配置文件: config.yml")

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 设置配置文件环境变量
    set_config_environment(args)
    
    print("="*60)
    print("Django服务器启动脚本 - 支持自定义配置文件")
    print("="*60)
    
    # 应用兼容性补丁
    print("正在应用兼容性补丁...")
    
    # 1. 直接使用collections.abc模块，避免过时警告
    import collections.abc
    print("✓ collections.abc模块已导入")
    
    # 2. 确保PyMySQL已安装
    try:
        import pymysql
        pymysql.version_info = (1, 4, 6, 'final', 0)  # 伪装成MySQLdb
        pymysql.install_as_MySQLdb()
        print("✓ PyMySQL已安装为MySQLdb")
    except ImportError:
        print("❌ 错误：未找到PyMySQL包，请安装：pip install PyMySQL==1.0.2")
        sys.exit(1)
    
    # 3. 导入Django MySQL兼容性补丁
    try:
        import django_mysql_patch
        print("✓ Django MySQL补丁已应用")
    except ImportError:
        print("⚠️ 警告：未找到Django MySQL兼容性补丁文件")
    
    # 导入日志配置
    try:
        from src.config.logging_config import configure_logging
        # 使用web类型的日志配置
        configure_logging(service_type='web')
        print("✓ 日志配置已加载")
    except ImportError as e:
        print(f"⚠️ 警告：日志配置加载失败: {e}")
    
    # 设置Django环境
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'src.config.settings')
    
    # 导入Django相关模块
    try:
        import django
        django.setup()
        from django.core.management import call_command
        print("✓ Django环境已成功设置")
        
        # 测试配置加载
        try:
            from src.config.config_loader import config
            print(f"✓ 配置文件已加载: {config.config_file}")
            print(f"  - 数据库主机: {config.get('database.host', '未配置')}")
            print(f"  - ES主机: {config.get('elasticsearch.hosts', ['未配置'])[0] if config.get('elasticsearch.hosts') else '未配置'}")
        except Exception as e:
            print(f"⚠️ 配置加载警告: {e}")
        
        # 初始化ES客户端
        try:
            from src.utils.es_client import es_client
            print(f"✓ Elasticsearch客户端已初始化")
        except ImportError as e:
            print(f"⚠️ 警告：导入ES客户端失败: {str(e)}")
        
    except ImportError as e:
        print(f"❌ 导入Django模块失败: {str(e)}")
        print("请确保已安装Django并且项目结构正确")
        sys.exit(1)
    
    # 启动服务器
    print("="*60)
    print("启动Django服务器...")
    print(f"服务器地址: http://{args.host}:{args.port}")
    print(f"当前工作目录: {os.getcwd()}")
    print("按 Ctrl+C 停止服务器")
    print("="*60)
    
    try:
        # 运行Django开发服务器
        call_command('runserver', f'{args.host}:{args.port}')
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
