#!/usr/bin/env python
# 手动运行任务，处理所有系统的数据

import os
import sys
import json
import logging
import logging.handlers
from datetime import datetime, timedelta

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'src.config.settings')

# 配置日志
def configure_logging():
    """配置日志"""
    # 创建日志目录
    logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs', 'task')
    os.makedirs(logs_dir, exist_ok=True)

    # 完全重置日志系统，确保与Django日志系统完全隔离
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.WARNING)  # 设置根日志级别为WARNING，减少干扰

    # 清除根日志记录器的所有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 创建独立的任务日志记录器
    logger = logging.getLogger('task')
    logger.setLevel(logging.INFO)
    logger.propagate = False  # 完全禁止日志传播到根日志记录器

    # 清除现有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # 文件处理器 - 使用独立的任务日志文件，避免与Django日志冲突
    log_file = os.path.join(logs_dir, 'task-execution.log')
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=20 * 1024 * 1024,  # 20MB
        backupCount=5,
        encoding='utf-8',
        delay=True  # 延迟创建文件，减少冲突
    )
    file_handler.setLevel(logging.INFO)

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # 格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器到任务日志记录器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    # 禁用其他可能的日志记录器，确保完全隔离
    logging.getLogger('django').setLevel(logging.CRITICAL)
    logging.getLogger('elasticsearch').setLevel(logging.CRITICAL)
    logging.getLogger('urllib3').setLevel(logging.CRITICAL)

    return logger

# 创建日志记录器
logger = configure_logging()

# 禁用Django的日志配置
os.environ['DISABLE_DJANGO_LOGGING'] = 'True'

import django
django.setup()

# 初始化ES客户端
try:
    from src.utils.es_client import es_client
    logger.info(f"Elasticsearch客户端已初始化，连接到: {es_client.transport.hosts}")
except Exception as e:
    logger.error(f"初始化ES客户端时出错: {e}")

from django.utils import timezone
from src.apps.tasks.tasks import user_behavior_analysis_task
from src.apps.tasks.models import ScheduledTask, TaskExecutionLog

def run_task_with_all_systems():
    """手动运行任务，处理所有系统的数据"""
    logger.info("手动运行任务，处理所有系统的数据...")

    # 获取昨天的日期
    yesterday = (timezone.now() - timedelta(days=1)).date()

    # 设置任务参数
    params = {
        'force_date': yesterday.strftime('%Y-%m-%d'),
        'all_systems': True
    }

    logger.info(f"任务参数: {params}")

    # 创建任务执行日志
    task = ScheduledTask.objects.get(task_type='user_behavior_analysis')
    start_time = timezone.now()
    log = TaskExecutionLog.objects.create(
        task=task,
        start_time=start_time,
        status='running'
    )

    try:
        # 执行任务
        result = user_behavior_analysis_task(params)

        # 更新任务执行日志
        end_time = timezone.now()
        log.end_time = end_time
        log.status = 'success'
        log.affected_rows = result.get('affected_rows', 0)
        log.execution_details = json.dumps(result)
        log.save()

        logger.info(f"任务执行成功，结果: {result}")

    except Exception as e:
        # 更新任务执行日志为失败状态
        end_time = timezone.now()
        log.end_time = end_time
        log.status = 'failed'
        log.error_message = str(e)
        log.save()

        logger.error(f"任务执行失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    run_task_with_all_systems()
