#!/usr/bin/env python
# 访问统计分析API

import logging
import json
from datetime import datetime, timedelta
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
from elasticsearch_dsl import Search, Q, A
from src.utils.es_client import es_client
from src.config.constants import ElasticsearchIndices

logger = logging.getLogger(__name__)

@api_view(['GET'])
def access_stats(request):
    """
    获取用户访问统计数据

    该接口用于挖掘用户在指定时间范围内每分钟访问某个requestPath的次数汇总。
    时间范围限制在1分钟到24小时之间。
    结果按时间升序排序并分页展示。

    查询参数:
    - start_time: 开始时间 (格式: YYYY-MM-DD HH:MM:SS)（必填）
    - end_time: 结束时间 (格式: YYYY-MM-DD HH:MM:SS)（必填）
    - account_id: 账号ID（必填）
    - request_path: 请求路径（必填）
    - min_count: 最小访问次数，只返回大于等于这个数的结果（选填，默认为1）
    - page: 页码，从1开始（选填，默认为1）
    - page_size: 每页记录数（选填，默认为10，最大为100）
    - system_type: 系统类型，可选值为api(默认)、oss、boss

    返回:
    - data: 符合条件的访问统计数据列表，每项包含timestamp（时间戳）和count（访问次数）
    - pagination: 分页信息，包含page（当前页码）、page_size（每页记录数）、total_count（总记录数）和total_pages（总页数）
    - summary: 查询摘要，包含查询参数和结果统计
    - message: 提示信息（仅当没有数据时显示）

    示例请求:
    GET /api/analytics/access-stats/?start_time=2025-04-24%2000:00:00&end_time=2025-04-24%2001:00:00&account_id=admin&request_path=/api/users/&min_count=1

    示例响应:
    {
        "data": [
            {"timestamp": "2025-04-24 00:01:00", "count": 5},
            {"timestamp": "2025-04-24 00:02:00", "count": 8},
            {"timestamp": "2025-04-24 00:03:00", "count": 12},
            ...
        ],
        "pagination": {
            "page": 1,
            "page_size": 10,
            "total_count": 35,
            "total_pages": 4
        },
        "summary": {
            "account_id": "admin",
            "request_path": "/api/users/",
            "start_time": "2025-04-24 00:00:00",
            "end_time": "2025-04-24 01:00:00",
            "min_count": 1,
            "total_minutes": 35,
            "system_type": "api"
        }
    }
    """
    try:
        # 获取必填参数
        start_time = request.query_params.get('start_time')
        end_time = request.query_params.get('end_time')
        account_id = request.query_params.get('account_id')
        request_path = request.query_params.get('request_path')
        system_type = request.query_params.get('system_type', 'api')  # 添加system_type参数

        # 验证必填参数
        if not all([start_time, end_time, account_id, request_path]):
            return Response(
                {'error': '缺少必填参数: start_time, end_time, account_id, request_path 都是必填的'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 解析时间参数
        try:
            start_datetime = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
            end_datetime = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return Response(
                {'error': '时间格式不正确，正确格式为: YYYY-MM-DD HH:MM:SS'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 验证时间范围
        time_diff = end_datetime - start_datetime
        if time_diff.total_seconds() < 60:  # 至少1分钟
            return Response(
                {'error': '时间范围至少需要1分钟'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if time_diff.total_seconds() > 86400:  # 最多24小时
            return Response(
                {'error': '时间范围最多为24小时'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 获取选填参数
        try:
            min_count = int(request.query_params.get('min_count', '1'))
            if min_count < 1:
                min_count = 1
        except ValueError:
            min_count = 1

        # 获取分页参数
        try:
            page = int(request.query_params.get('page', '1'))
            if page < 1:
                page = 1
        except ValueError:
            page = 1

        try:
            page_size = int(request.query_params.get('page_size', '10'))
            if page_size < 1:
                page_size = 10
            elif page_size > 100:  # 限制最大每页数量
                page_size = 100
        except ValueError:
            page_size = 10

        # 构建ES查询
        try:
            # 获取ES索引和字段映射
            log_index = ElasticsearchIndices.get_operation_log(system_type)
            field_mapping = ElasticsearchIndices.get_field_mapping(system_type)
            logger.info(f"使用日志索引: {log_index}, 系统类型: {system_type}")

            # 构建基本查询
            s = Search(using=es_client, index=log_index)

            # 添加过滤条件
            # 对于boss系统，使用match_phrase查询operateUserNameAnalyzed字段
            if system_type == 'boss':
                s = s.query('match_phrase', operateUserNameAnalyzed=account_id)
            else:
                s = s.filter('term', **{field_mapping['user_id']: account_id})

            s = s.filter('term', **{field_mapping['request_path']: request_path})

            # 添加时间范围过滤
            s = s.filter('range', **{field_mapping['create_time']: {
                'gte': start_time,
                'lte': end_time
            }})

            # 添加按分钟聚合
            s.aggs.bucket('per_minute', 'date_histogram',
                field=field_mapping['create_time'],
                interval='1m',
                format='yyyy-MM-dd HH:mm:ss'
            )

            # 设置size为0，因为我们只关心聚合结果
            s = s.extra(size=0)

            # 执行查询
            logger.info(f"执行访问统计查询: {s.to_dict()}")

            # 添加重试机制
            retry_count = 0
            max_retries = 3
            while retry_count < max_retries:
                try:
                    response = s.execute()
                    break
                except Exception as e:
                    retry_count += 1
                    if retry_count >= max_retries:
                        raise
                    logger.warning(f"执行查询失败，正在重试 ({retry_count}/{max_retries}): {str(e)}")
                    import time
                    time.sleep(2)  # 等待2秒后重试

            # 处理聚合结果
            minute_stats = []

            if hasattr(response, 'aggregations') and hasattr(response.aggregations, 'per_minute'):
                for bucket in response.aggregations.per_minute.buckets:
                    # 只保留大于等于min_count的结果
                    if bucket.doc_count >= min_count:
                        minute_stats.append({
                            'timestamp': bucket.key_as_string,
                            'count': bucket.doc_count
                        })

            # 按时间升序排序
            minute_stats.sort(key=lambda x: x['timestamp'])

            # 计算总数
            total_count = len(minute_stats)

            # 分页处理
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size

            # 获取当前页的数据
            page_data = minute_stats[start_idx:end_idx] if start_idx < total_count else []

            # 计算总页数
            total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 0

            # 构建返回结果
            result = {
                'data': page_data,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'total_pages': total_pages
                },
                'summary': {
                    'account_id': account_id,
                    'request_path': request_path,
                    'start_time': start_time,
                    'end_time': end_time,
                    'min_count': min_count,
                    'total_minutes': total_count,
                    'system_type': system_type
                }
            }

            # 如果没有数据，添加提示信息
            if total_count == 0:
                result['message'] = '没有找到符合条件的数据。请检查查询参数或调整时间范围。'

            return Response(result)

        except Exception as e:
            logger.error(f"查询访问统计数据失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return Response(
                {'error': f'查询失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    except Exception as e:
        logger.error(f"处理请求失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return Response(
            {'error': f'处理请求失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
