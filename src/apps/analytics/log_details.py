#!/usr/bin/env python
# 日志记录明细API

import logging
from datetime import datetime
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings
from elasticsearch_dsl import Search, Q
from src.utils.es_client import es_client
from src.config.constants import ElasticsearchIndices

logger = logging.getLogger(__name__)

@api_view(['GET'])
def log_details(request):
    """
    查询日志记录明细

    查询参数:
    - account_id: 账号ID（对应ES中的accountId字段）
    - request_path: 请求路径（对应ES中的requestPath字段）
    - create_time_start: 创建时间开始 (格式: YYYY-MM-DD HH:MM:SS)（对应ES中的createTime字段）
    - create_time_end: 创建时间结束 (格式: YYYY-MM-DD HH:MM:SS)（对应ES中的createTime字段）
    - operate_type: 操作类型（对应ES中的operateType字段）
    - result: 结果（对应ES中的result字段）
    - min_cost: 最小耗时（毫秒）（对应ES中的cost字段）
    - page: 页码，从1开始
    - page_size: 每页记录数
    """
    try:
        # 获取查询参数
        account_id = request.query_params.get('account_id')  # accountId
        request_path = request.query_params.get('request_path')  # requestPath
        create_time_start = request.query_params.get('create_time_start')  # createTime
        create_time_end = request.query_params.get('create_time_end')  # createTime
        operate_type = request.query_params.get('operate_type')  # operateType
        result = request.query_params.get('result')  # result
        min_cost = request.query_params.get('min_cost')  # cost
        system_type = request.query_params.get('system_type', 'api')  # 系统类型，默认为api

        # 转换min_cost为整数
        if min_cost:
            try:
                min_cost = int(min_cost)
            except ValueError:
                return Response(
                    {'error': 'min_cost必须是整数'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # 获取分页参数
        try:
            page = int(request.query_params.get('page', '1'))
            if page < 1:
                page = 1
        except ValueError:
            page = 1

        try:
            page_size = int(request.query_params.get('page_size', '10'))
            if page_size < 1:
                page_size = 10
            elif page_size > 100:  # 限制最大每页数量
                page_size = 100
        except ValueError:
            page_size = 10

        try:
            # 使用常量类中定义的日志索引
            log_index = ElasticsearchIndices.get_operation_log(system_type)
            logger.info(f"使用日志索引: {log_index}, 系统类型: {system_type}")

            # 获取字段映射
            field_mapping = ElasticsearchIndices.get_field_mapping(system_type)

            # 构建ES查询
            s = Search(using=es_client, index=log_index)

            # 添加过滤条件
            if account_id:
                # 对于boss系统，使用operateUserNameAnalyzed字段进行文本搜索
                if system_type == 'boss':
                    # 使用match_phrase查询精确匹配operateUserNameAnalyzed
                    s = s.query('match_phrase', operateUserNameAnalyzed=account_id)
                    logger.info(f"Boss系统使用operateUserNameAnalyzed字段进行短语匹配: {account_id}")
                else:
                    s = s.filter('term', **{field_mapping['user_id']: account_id})

            if request_path:
                s = s.filter('wildcard', **{field_mapping['request_path']: f'*{request_path}*'})

            # 处理时间范围
            time_range = {}
            if create_time_start:
                time_range['gte'] = create_time_start
            if create_time_end:
                time_range['lte'] = create_time_end

            if time_range:
                s = s.filter('range', **{field_mapping['create_time']: time_range})

            if operate_type:
                s = s.filter('term', **{field_mapping['operate_type']: operate_type})

            if result:
                s = s.filter('term', **{field_mapping['result']: result})

            if min_cost is not None:
                s = s.filter('range', **{field_mapping['cost']: {'gte': min_cost}})

            # 排序
            s = s.sort(f"-{field_mapping['create_time']}")

            # 获取总数
            try:
                # 添加重试机制
                retry_count = 0
                max_retries = 3
                while retry_count < max_retries:
                    try:
                        total = s.count()
                        logger.info(f"查询到总数: {total}")
                        break
                    except Exception as e:
                        retry_count += 1
                        if retry_count >= max_retries:
                            raise
                        logger.warning(f"查询总数失败，正在重试 ({retry_count}/{max_retries}): {str(e)}")
                        import time
                        time.sleep(2)  # 等待2秒后重试
            except Exception as e:
                logger.error(f"查询总数失败: {str(e)}")
                total = 0  # 如果查询失败，设置为0

            # 限制查询结果数量
            max_size = settings.ELASTICSEARCH_QUERY_CONFIG.get('size_limit', 10000)

            # 只在用户尝试访问超过限制的页面时返回错误提示，但使用200状态码
            if (page-1)*page_size >= max_size:
                return Response(
                    {
                        'error': f'访问的页面超过{max_size}条数据限制，请添加更多搜索条件缩小查询范围或访问前{max_size//page_size}页的数据。\n\n您可以尝试以下方法：\n1. 添加时间范围限制\n2. 指定特定的账号ID\n3. 指定特定的请求路径\n4. 指定特定的操作类型\n5. 指定特定的结果状态',
                        'total': total,
                        'max_size': max_size,
                        'max_page': max_size//page_size,
                        'results': []  # 返回空结果列表
                    },
                    status=status.HTTP_200_OK  # 返回200状态码
                )

            # 如果总数超过限制，在响应中添加警告信息
            warning_message = None
            if total > max_size:
                warning_message = f'查询结果总数为{total}条，超过{max_size}条限制，只能访问前{max_size//page_size}页的数据'

            # 处理分页
            scroll_timeout = settings.ELASTICSEARCH_QUERY_CONFIG.get('scroll_timeout', '1m')

            # 正常分页
            s = s[(page-1)*page_size:page*page_size]

            # 执行查询
            query_dict = s.to_dict()
            logger.info(f"执行日志记录明细查询: {query_dict}")

            # 添加重试机制
            retry_count = 0
            max_retries = 3
            while retry_count < max_retries:
                try:
                    response = s.execute()
                    break
                except Exception as e:
                    retry_count += 1
                    if retry_count >= max_retries:
                        raise
                    logger.warning(f"执行查询失败，正在重试 ({retry_count}/{max_retries}): {str(e)}")
                    import time
                    time.sleep(2)  # 等待2秒后重试

            # 处理结果
            results = []

            # 处理查询返回的结果
            for hit in response:
                # 将hit转换为字典
                item = hit.to_dict()

                # 获取ES的_id字段
                es_id = hit.meta.id if hasattr(hit, 'meta') and hasattr(hit.meta, 'id') else None

                # 创建一个新的标准化字典，使用api系统的字段名称
                standardized_item = {}

                # 映射字段名称
                field_mapping_reverse = {
                    'cost': 'cost',
                    'ipAddress': 'ipAddress',
                    'operateType': 'operateType',
                    'accountId': 'accountId',
                    'result': 'result',
                    'createTime': 'createTime',
                    'requestPath': 'requestPath',
                    'className': 'className',
                    'methodName': 'methodName',
                    'methodParam': 'methodParam',
                    'methodReturn': 'methodReturn'
                }

                # 获取字段映射
                api_to_current = {}
                for api_field, current_field in field_mapping.items():
                    if api_field == 'user_id':
                        api_to_current['accountId'] = current_field
                    elif api_field == 'ip':
                        api_to_current['ipAddress'] = current_field
                    elif api_field == 'cost':
                        api_to_current['cost'] = current_field
                    elif api_field == 'operate_type':
                        api_to_current['operateType'] = current_field
                    elif api_field == 'result':
                        api_to_current['result'] = current_field
                    elif api_field == 'create_time':
                        api_to_current['createTime'] = current_field
                    elif api_field == 'request_path':
                        api_to_current['requestPath'] = current_field
                    elif api_field == 'class_name':
                        api_to_current['className'] = current_field
                    elif api_field == 'method_name':
                        api_to_current['methodName'] = current_field
                    elif api_field == 'method_param':
                        api_to_current['methodParam'] = current_field

                # 映射字段值
                for api_field, current_field in api_to_current.items():
                    if current_field in item:
                        standardized_item[api_field] = item[current_field]

                # 特殊处理：当system_type为boss时，将operateUserNameAnalyzed映射为accountId
                if system_type == 'boss':
                    # 将operateUserNameAnalyzed映射为accountId
                    if 'operateUserNameAnalyzed' in item:
                        standardized_item['accountId'] = item['operateUserNameAnalyzed']
                    # 将requestNameAnalyzed映射为methodName
                    if 'requestNameAnalyzed' in item:
                        standardized_item['methodName'] = item['requestNameAnalyzed']
                    # 将requestParam映射为methodParam
                    if 'requestParam' in item:
                        standardized_item['methodParam'] = item['requestParam']

                # 处理其他可能存在的字段
                for field in item:
                    if field not in api_to_current.values() and field not in standardized_item:
                        standardized_item[field] = item[field]

                # 格式化时间
                create_time_field = field_mapping['create_time']
                if create_time_field in item and item[create_time_field]:
                    try:
                        # 尝试解析时间字符串
                        if isinstance(item[create_time_field], str):
                            dt = datetime.fromisoformat(item[create_time_field].replace('Z', '+00:00'))
                            formatted_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                            standardized_item['createTime'] = formatted_time
                    except Exception as e:
                        logger.warning(f"时间格式化失败: {str(e)}")

                # 添加id字段
                if es_id:
                    standardized_item['id'] = es_id

                # 使用标准化的字典替代原始字典
                item = standardized_item

                results.append(item)

            # 返回结果
            response_data = {
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size if total > 0 else 0,
                'system_type': system_type,
                'results': results
            }

            # 如果有警告信息，添加到响应中
            if warning_message:
                response_data['warning'] = warning_message
                response_data['max_size'] = max_size
                response_data['max_page'] = max_size // page_size

            return Response(response_data)
        except Exception as e:
            logger.error(f"查询日志记录明细失败: {str(e)}")
            return Response(
                {'error': f'查询失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    except Exception as e:
        return Response(
            {'error': f'查询失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
