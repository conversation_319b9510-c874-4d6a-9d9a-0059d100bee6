# 时间维度分析模块模型

from django.db import models

class DailyStatistics(models.Model):
    """
    日统计数据表
    存储按日汇总的统计数据
    """
    stat_date = models.DateField(verbose_name='统计日期')
    dimension = models.CharField(max_length=50, verbose_name='统计维度')
    dimension_value = models.CharField(max_length=100, verbose_name='维度值')
    metric_name = models.CharField(max_length=50, verbose_name='指标名称')
    metric_value = models.FloatField(verbose_name='指标值')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'daily_statistics'
        unique_together = ('stat_date', 'dimension', 'dimension_value', 'metric_name')
        verbose_name = '日统计数据'
        verbose_name_plural = verbose_name

    def __str__(self):
        return f'{self.stat_date}:{self.dimension}:{self.dimension_value}:{self.metric_name}'


class WeeklyStatistics(models.Model):
    """
    周统计数据表
    存储按周汇总的统计数据
    """
    year_week = models.CharField(max_length=7, verbose_name='年周')
    start_date = models.DateField(verbose_name='开始日期')
    end_date = models.DateField(verbose_name='结束日期')
    dimension = models.CharField(max_length=50, verbose_name='统计维度')
    dimension_value = models.CharField(max_length=100, verbose_name='维度值')
    metric_name = models.CharField(max_length=50, verbose_name='指标名称')
    metric_value = models.FloatField(verbose_name='指标值')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'weekly_statistics'
        unique_together = ('year_week', 'dimension', 'dimension_value', 'metric_name')
        verbose_name = '周统计数据'
        verbose_name_plural = verbose_name

    def __str__(self):
        return f'{self.year_week}:{self.dimension}:{self.dimension_value}:{self.metric_name}'


class MonthlyStatistics(models.Model):
    """
    月统计数据表
    存储按月汇总的统计数据
    """
    year_month = models.CharField(max_length=7, verbose_name='年月')
    dimension = models.CharField(max_length=50, verbose_name='统计维度')
    dimension_value = models.CharField(max_length=100, verbose_name='维度值')
    metric_name = models.CharField(max_length=50, verbose_name='指标名称')
    metric_value = models.FloatField(verbose_name='指标值')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'monthly_statistics'
        unique_together = ('year_month', 'dimension', 'dimension_value', 'metric_name')
        verbose_name = '月统计数据'
        verbose_name_plural = verbose_name

    def __str__(self):
        return f'{self.year_month}:{self.dimension}:{self.dimension_value}:{self.metric_name}'


class UserOperateAnalysisDay(models.Model):
    """
    用户操作分析日统计表
    对应数据库表: tgt_api_user_operate_analysis_day
    """
    id = models.BigAutoField(primary_key=True, verbose_name='主键id')
    request_url = models.CharField(max_length=256, null=True, blank=True, verbose_name='操作路径url')
    request_user_id = models.CharField(max_length=64, null=True, blank=True, verbose_name='操作人id')
    request_url_name = models.CharField(max_length=255, null=True, blank=True, verbose_name='操作路径名称')
    request_type = models.CharField(max_length=32, verbose_name='业务类型')
    avg_time = models.IntegerField(null=True, blank=True, verbose_name='平均操作时间')
    request_count = models.IntegerField(null=True, blank=True, verbose_name='操作次数')
    day = models.DateField(verbose_name='统计时间')
    created_time = models.DateTimeField(null=True, blank=True, verbose_name='创建时间')
    system_type = models.CharField(max_length=20, default='api', verbose_name='系统类型')

    class Meta:
        db_table = 'tgt_api_user_operate_analysis_day'
        verbose_name = '用户操作分析日统计'
        verbose_name_plural = verbose_name
        # 表中定义的索引
        indexes = [
            models.Index(fields=['day'], name='day'),
            models.Index(fields=['system_type'], name='system_type'),
            models.Index(fields=['day', 'system_type'], name='day_system_type'),
        ]

    def __str__(self):
        return f"{self.request_url} - {self.request_user_id} - {self.day}"