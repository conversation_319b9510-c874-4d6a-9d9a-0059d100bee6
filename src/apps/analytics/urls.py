# 时间维度分析模块URL配置

from django.urls import path
from . import views
from .dashboard import DashboardViewSet
from .log_details import log_details
from .access_stats import access_stats
from .performance_stats import slow_functions

urlpatterns = [
    # 日维度统计
    path('daily/', views.TimeAnalysisViewSet.as_view({'get': 'get_daily_stats'}), name='analytics-daily'),
    # 周维度统计
    path('weekly/', views.TimeAnalysisViewSet.as_view({'get': 'get_weekly_stats'}), name='analytics-weekly'),
    # 月维度统计
    path('monthly/', views.TimeAnalysisViewSet.as_view({'get': 'get_monthly_stats'}), name='analytics-monthly'),

    # 首页仪表盘
    path('dashboard/overview/', DashboardViewSet.as_view({'get': 'overview'}), name='dashboard-overview'),
    path('dashboard/active-users/', DashboardViewSet.as_view({'get': 'active_users'}), name='dashboard-active-users'),
    path('dashboard/function-calls/', DashboardViewSet.as_view({'get': 'function_calls'}), name='dashboard-function-calls'),
    path('dashboard/avg-response-time/', DashboardViewSet.as_view({'get': 'avg_response_time'}), name='dashboard-avg-response-time'),
    path('dashboard/error-rate/', DashboardViewSet.as_view({'get': 'error_rate'}), name='dashboard-error-rate'),
    path('dashboard/tps-and-response-time/', DashboardViewSet.as_view({'get': 'tps_and_response_time'}), name='dashboard-tps-and-response-time'),
    path('dashboard/interface-tps-and-records/', DashboardViewSet.as_view({'get': 'interface_tps_and_records'}), name='dashboard-interface-tps-and-records'),
    path('dashboard/operation-logs/', DashboardViewSet.as_view({'get': 'operation_logs'}), name='dashboard-operation-logs'),
    path('dashboard/user-behavior-analysis/', DashboardViewSet.as_view({'get': 'user_behavior_analysis'}), name='dashboard-user-behavior-analysis'),
    path('dashboard/feature-operation-analysis/', DashboardViewSet.as_view({'get': 'feature_operation_analysis'}), name='dashboard-feature-operation-analysis'),
    path('dashboard/feature-usage-stats/', DashboardViewSet.as_view({'get': 'feature_usage_stats'}), name='dashboard-feature-usage-stats'),

    # 日志记录明细
    path('log-details/', log_details, name='log-details'),

    # 访问统计分析
    path('access-stats/', access_stats, name='access-stats'),

    # 耗时功能排行
    path('slow-functions/', slow_functions, name='slow-functions'),
]