# 时间维度分析模块视图

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from elasticsearch_dsl import Search, Q
from datetime import datetime, timedelta
import pandas as pd

from .models import DailyStatistics, WeeklyStatistics, MonthlyStatistics
from src.utils.es_client import es_client
from src.utils.date_utils import get_date_range
from src.config.constants import ElasticsearchIndices


class TimeAnalysisViewSet(viewsets.ViewSet):
    """
    时间维度分析视图集
    提供不同时间粒度的数据统计和趋势分析
    """
    
    def get_daily_stats(self, request):
        """
        获取日维度统计数据
        支持多种维度和指标的日统计数据查询
        """
        # 获取参数
        dimension = request.query_params.get('dimension', 'user')
        metric = request.query_params.get('metric', 'count')
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        top_n = int(request.query_params.get('top_n', 10))
        
        # 获取日期范围
        start_date, end_date = get_date_range('daily', start_date, end_date, default_days=30)
        
        # 尝试从统计表获取数据
        try:
            # 查询日期范围内的统计数据
            stats = DailyStatistics.objects.filter(
                stat_date__gte=start_date.date(),
                stat_date__lte=end_date.date(),
                dimension=dimension,
                metric_name=metric
            ).order_by('stat_date', '-metric_value')[:top_n * 100]  # 获取足够多的数据以便后续处理
            
            # 如果有数据，直接返回
            if stats.exists():
                # 按日期分组，每天取top_n
                result_data = {}
                for stat in stats:
                    date_str = stat.stat_date.strftime('%Y-%m-%d')
                    if date_str not in result_data:
                        result_data[date_str] = []
                    
                    if len(result_data[date_str]) < top_n:
                        result_data[date_str].append({
                            'dimension_value': stat.dimension_value,
                            'metric_value': stat.metric_value
                        })
                
                # 格式化结果
                result = {
                    'dimension': dimension,
                    'metric': metric,
                    'date_range': {
                        'start': start_date.date().isoformat(),
                        'end': end_date.date().isoformat(),
                    },
                    'data': [{
                        'date': date,
                        'values': values
                    } for date, values in result_data.items()]
                }
                
                return Response(result)
        except Exception as e:
            # 如果出错，从ES查询
            pass
        
        # 从ES查询数据
        # 构建ES查询
        s = Search(using=es_client, index=ElasticsearchIndices.OPERATION_LOG_WILDCARD) \
            .filter('range', requestTime={'gte': start_date, 'lte': end_date})
        
        # 按日期和维度聚合
        s.aggs.bucket('by_date', 'date_histogram', field='requestTime', calendar_interval='day') \
            .bucket('by_dimension', 'terms', field=self._get_dimension_field(dimension), size=top_n)
        
        # 如果是性能指标，添加指标聚合
        if metric == 'avg_time':
            s.aggs['by_date']['by_dimension'].metric('metric_value', 'avg', field='costTime')
        elif metric == 'max_time':
            s.aggs['by_date']['by_dimension'].metric('metric_value', 'max', field='costTime')
        elif metric == 'error_rate':
            s.aggs['by_date']['by_dimension'].bucket('errors', 'filter', filter=Q('term', operateResult='失败'))
        
        # 执行查询
        response = s.execute()
        
        # 处理结果
        result = {
            'dimension': dimension,
            'metric': metric,
            'date_range': {
                'start': start_date.date().isoformat(),
                'end': end_date.date().isoformat(),
            },
            'data': []
        }
        
        # 处理聚合结果
        for date_bucket in response.aggregations.by_date.buckets:
            date_data = {
                'date': date_bucket.key_as_string[:10],  # 只取日期部分
                'values': []
            }
            
            for dim_bucket in date_bucket.by_dimension.buckets:
                # 根据指标类型获取值
                if metric == 'count':
                    metric_value = dim_bucket.doc_count
                elif metric in ['avg_time', 'max_time']:
                    metric_value = dim_bucket.metric_value.value
                elif metric == 'error_rate':
                    total = dim_bucket.doc_count
                    errors = dim_bucket.errors.doc_count
                    metric_value = round(errors / total * 100, 2) if total > 0 else 0
                else:
                    metric_value = 0
                
                date_data['values'].append({
                    'dimension_value': dim_bucket.key,
                    'metric_value': metric_value
                })
            
            result['data'].append(date_data)
        
        return Response(result)
    
    def get_weekly_stats(self, request):
        """
        获取周维度统计数据
        支持多种维度和指标的周统计数据查询
        """
        # 获取参数
        dimension = request.query_params.get('dimension', 'user')
        metric = request.query_params.get('metric', 'count')
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        top_n = int(request.query_params.get('top_n', 10))
        
        # 获取日期范围
        start_date, end_date = get_date_range('weekly', start_date, end_date, default_days=90)
        
        # 尝试从统计表获取数据
        try:
            # 计算周范围
            start_week = start_date.strftime('%Y-%W')
            end_week = end_date.strftime('%Y-%W')
            
            # 查询周范围内的统计数据
            stats = WeeklyStatistics.objects.filter(
                year_week__gte=start_week,
                year_week__lte=end_week,
                dimension=dimension,
                metric_name=metric
            ).order_by('year_week', '-metric_value')[:top_n * 20]  # 获取足够多的数据以便后续处理
            
            # 如果有数据，直接返回
            if stats.exists():
                # 按周分组，每周取top_n
                result_data = {}
                for stat in stats:
                    if stat.year_week not in result_data:
                        result_data[stat.year_week] = []
                    
                    if len(result_data[stat.year_week]) < top_n:
                        result_data[stat.year_week].append({
                            'dimension_value': stat.dimension_value,
                            'metric_value': stat.metric_value
                        })
                
                # 格式化结果
                result = {
                    'dimension': dimension,
                    'metric': metric,
                    'date_range': {
                        'start': start_date.date().isoformat(),
                        'end': end_date.date().isoformat(),
                    },
                    'data': [{
                        'week': week,
                        'values': values
                    } for week, values in result_data.items()]
                }
                
                return Response(result)
        except Exception as e:
            # 如果出错，从ES查询
            pass
        
        # 从ES查询数据
        # 构建ES查询
        s = Search(using=es_client, index=ElasticsearchIndices.OPERATION_LOG_WILDCARD) \
            .filter('range', requestTime={'gte': start_date, 'lte': end_date})
        
        # 按周和维度聚合
        s.aggs.bucket('by_week', 'date_histogram', field='requestTime', calendar_interval='week') \
            .bucket('by_dimension', 'terms', field=self._get_dimension_field(dimension), size=top_n)
        
        # 如果是性能指标，添加指标聚合
        if metric == 'avg_time':
            s.aggs['by_week']['by_dimension'].metric('metric_value', 'avg', field='costTime')
        elif metric == 'max_time':
            s.aggs['by_week']['by_dimension'].metric('metric_value', 'max', field='costTime')
        elif metric == 'error_rate':
            s.aggs['by_week']['by_dimension'].bucket('errors', 'filter', filter=Q('term', operateResult='失败'))
        
        # 执行查询
        response = s.execute()
        
        # 处理结果
        result = {
            'dimension': dimension,
            'metric': metric,
            'date_range': {
                'start': start_date.date().isoformat(),
                'end': end_date.date().isoformat(),
            },
            'data': []
        }
        
        # 处理聚合结果
        for week_bucket in response.aggregations.by_week.buckets:
            # 计算年周
            week_date = datetime.fromisoformat(week_bucket.key_as_string.replace('Z', '+00:00'))
            year_week = week_date.strftime('%Y-%W')
            
            week_data = {
                'week': year_week,
                'start_date': week_date.strftime('%Y-%m-%d'),
                'values': []
            }
            
            for dim_bucket in week_bucket.by_dimension.buckets:
                # 根据指标类型获取值
                if metric == 'count':
                    metric_value = dim_bucket.doc_count
                elif metric in ['avg_time', 'max_time']:
                    metric_value = dim_bucket.metric_value.value
                elif metric == 'error_rate':
                    total = dim_bucket.doc_count
                    errors = dim_bucket.errors.doc_count
                    metric_value = round(errors / total * 100, 2) if total > 0 else 0
                else:
                    metric_value = 0
                
                week_data['values'].append({
                    'dimension_value': dim_bucket.key,
                    'metric_value': metric_value
                })
            
            result['data'].append(week_data)
        
        return Response(result)
    
    def get_monthly_stats(self, request):
        """
        获取月维度统计数据
        支持多种维度和指标的月统计数据查询
        """
        # 获取参数
        dimension = request.query_params.get('dimension', 'user')
        metric = request.query_params.get('metric', 'count')
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        top_n = int(request.query_params.get('top_n', 10))
        
        # 获取日期范围
        start_date, end_date = get_date_range('monthly', start_date, end_date, default_days=365)
        
        # 尝试从统计表获取数据
        try:
            # 计算月范围
            start_month = start_date.strftime('%Y-%m')
            end_month = end_date.strftime('%Y-%m')
            
            # 查询月范围内的统计数据
            stats = MonthlyStatistics.objects.filter(
                year_month__gte=start_month,
                year_month__lte=end_month,
                dimension=dimension,
                metric_name=metric
            ).order_by('year_month', '-metric_value')[:top_n * 12]  # 获取足够多的数据以便后续处理
            
            # 如果有数据，直接返回
            if stats.exists():
                # 按月分组，每月取top_n
                result_data = {}
                for stat in stats:
                    if stat.year_month not in result_data:
                        result_data[stat.year_month] = []
                    
                    if len(result_data[stat.year_month]) < top_n:
                        result_data[stat.year_month].append({
                            'dimension_value': stat.dimension_value,
                            'metric_value': stat.metric_value
                        })
                
                # 格式化结果
                result = {
                    'dimension': dimension,
                    'metric': metric,
                    'date_range': {
                        'start': start_date.date().isoformat(),
                        'end': end_date.date().isoformat(),
                    },
                    'data': [{
                        'month': month,
                        'values': values
                    } for month, values in result_data.items()]
                }
                
                return Response(result)
        except Exception as e:
            # 如果出错，从ES查询
            pass
        
        # 从ES查询数据
        # 构建ES查询
        s = Search(using=es_client, index=ElasticsearchIndices.OPERATION_LOG_WILDCARD) \
            .filter('range', requestTime={'gte': start_date, 'lte': end_date})
        
        # 按月和维度聚合
        s.aggs.bucket('by_month', 'date_histogram', field='requestTime', calendar_interval='month') \
            .bucket('by_dimension', 'terms', field=self._get_dimension_field(dimension), size=top_n)
        
        # 如果是性能指标，添加指标聚合
        if metric == 'avg_time':
            s.aggs['by_month']['by_dimension'].metric('metric_value', 'avg', field='costTime')
        elif metric == 'max_time':
            s.aggs['by_month']['by_dimension'].metric('metric_value', 'max', field='costTime')
        elif metric == 'error_rate':
            s.aggs['by_month']['by_dimension'].bucket('errors', 'filter', filter=Q('term', operateResult='失败'))
        
        # 执行查询
        response = s.execute()
        
        # 处理结果
        result = {
            'dimension': dimension,
            'metric': metric,
            'date_range': {
                'start': start_date.date().isoformat(),
                'end': end_date.date().isoformat(),
            },
            'data': []
        }
        
        # 处理聚合结果
        for month_bucket in response.aggregations.by_month.buckets:
            # 计算年月
            month_date = datetime.fromisoformat(month_bucket.key_as_string.replace('Z', '+00:00'))
            year_month = month_date.strftime('%Y-%m')
            
            month_data = {
                'month': year_month,
                'values': []
            }
            
            for dim_bucket in month_bucket.by_dimension.buckets:
                # 根据指标类型获取值
                if metric == 'count':
                    metric_value = dim_bucket.doc_count
                elif metric in ['avg_time', 'max_time']:
                    metric_value = dim_bucket.metric_value.value
                elif metric == 'error_rate':
                    total = dim_bucket.doc_count
                    errors = dim_bucket.errors.doc_count
                    metric_value = round(errors / total * 100, 2) if total > 0 else 0
                else:
                    metric_value = 0
                
                month_data['values'].append({
                    'dimension_value': dim_bucket.key,
                    'metric_value': metric_value
                })
            
            result['data'].append(month_data)
        
        return Response(result)
    
    def _get_dimension_field(self, dimension):
        """
        根据维度名称获取对应的ES字段
        """
        dimension_mapping = {
            'user': 'operateUserId',
            'feature': 'requestUrl.keyword',
            'ip': 'ip.keyword'
        }
        return dimension_mapping.get(dimension, 'operateUserId')