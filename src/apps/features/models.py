# 功能分析模块模型

from django.db import models

class FeatureMapping(models.Model):
    """
    功能映射表
    将URL映射到具体功能描述
    """
    url_pattern = models.CharField(max_length=255, verbose_name='URL模式')
    feature_name = models.CharField(max_length=100, verbose_name='功能名称')
    feature_category = models.CharField(max_length=50, null=True, blank=True, verbose_name='功能类别')
    feature_description = models.TextField(null=True, blank=True, verbose_name='功能描述')
    is_core = models.BooleanField(default=False, verbose_name='是否核心功能')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'feature_mapping'
        verbose_name = '功能映射'
        verbose_name_plural = verbose_name
        
    def __str__(self):
        return self.feature_name