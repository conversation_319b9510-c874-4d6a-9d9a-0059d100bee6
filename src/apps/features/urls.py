# 功能分析模块URL配置

from django.urls import path
from . import views

urlpatterns = [
    # 功能使用情况分析
    path('usage/', views.FeatureAnalysisViewSet.as_view({'get': 'get_feature_usage'}), name='feature-usage'),
    # 功能性能分析
    path('performance/', views.FeatureAnalysisViewSet.as_view({'get': 'get_feature_performance'}), name='feature-performance'),
    # 功能使用趋势分析
    path('<int:feature_id>/trend/', views.FeatureAnalysisViewSet.as_view({'get': 'get_feature_trend'}), name='feature-trend'),
    # 功能质量分析
    path('quality/', views.FeatureAnalysisViewSet.as_view({'get': 'get_feature_quality'}), name='feature-quality'),
]