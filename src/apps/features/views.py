# 功能分析模块视图

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from elasticsearch_dsl import Search, Q, A
from datetime import datetime, timedelta
import pandas as pd

from .models import FeatureMapping
from src.utils.es_client import es_client
from src.utils.date_utils import get_date_range
from src.config.constants import ElasticsearchIndices


class FeatureAnalysisViewSet(viewsets.ViewSet):
    """
    功能分析视图集
    提供功能使用情况、性能和质量分析接口
    """
    
    def get_feature_usage(self, request):
        """
        获取功能使用情况
        统计各功能的访问量、使用频率等
        """
        # 获取参数
        time_dimension = request.query_params.get('dimension', 'daily')
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        top_n = int(request.query_params.get('top_n', 10))
        
        # 获取日期范围
        start_date, end_date = get_date_range(time_dimension, start_date, end_date)
        
        # 构建ES查询
        s = Search(using=es_client, index=ElasticsearchIndices.OPERATION_LOG_WILDCARD) \
            .filter('range', requestTime={'gte': start_date, 'lte': end_date})
        
        # 按URL聚合 - 使用兼容7.10.0版本的语法
        features_agg = A('terms', field='requestUrl.keyword', size=top_n)
        s.aggs.bucket('features', features_agg)
        
        # 执行查询
        response = s.execute()
        
        # 处理结果
        result = {
            'total_requests': response.hits.total.value,
            'date_range': {
                'start': start_date.isoformat(),
                'end': end_date.isoformat(),
            },
            'features': []
        }
        
        # 获取功能映射
        feature_mappings = {fm.url_pattern: fm.feature_name for fm in FeatureMapping.objects.all()}
        
        # 处理功能使用情况
        for bucket in response.aggregations.features.buckets:
            url = bucket.key
            # 尝试匹配功能名称
            feature_name = None
            for pattern, name in feature_mappings.items():
                if pattern in url:
                    feature_name = name
                    break
            
            result['features'].append({
                'url': url,
                'feature_name': feature_name or '未映射功能',
                'count': bucket.doc_count,
                'percentage': round(bucket.doc_count / result['total_requests'] * 100, 2)
            })
        
        return Response(result)
    
    def get_feature_performance(self, request):
        """
        获取功能性能指标
        分析功能响应时间等性能指标
        """
        # 获取参数
        time_dimension = request.query_params.get('dimension', 'daily')
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        top_n = int(request.query_params.get('top_n', 10))
        
        # 获取日期范围
        start_date, end_date = get_date_range(time_dimension, start_date, end_date)
        
        # 构建ES查询
        s = Search(using=es_client, index=ElasticsearchIndices.OPERATION_LOG_WILDCARD) \
            .filter('range', requestTime={'gte': start_date, 'lte': end_date})
        
        # 按URL聚合并计算响应时间统计 - 使用兼容7.10.0版本的语法
        features_agg = A('terms', field='requestUrl.keyword', size=top_n)
        s.aggs.bucket('features', features_agg)
        
        # 添加响应时间统计指标
        s.aggs['features'].metric('avg_time', A('avg', field='costTime'))
        s.aggs['features'].metric('max_time', A('max', field='costTime'))
        s.aggs['features'].metric('min_time', A('min', field='costTime'))
        s.aggs['features'].metric('percentiles', A('percentiles', field='costTime', percents=[50, 75, 90, 95, 99]))
        
        # 执行查询
        response = s.execute()
        
        # 处理结果
        result = {
            'date_range': {
                'start': start_date.isoformat(),
                'end': end_date.isoformat(),
            },
            'features': []
        }
        
        # 获取功能映射
        feature_mappings = {fm.url_pattern: fm.feature_name for fm in FeatureMapping.objects.all()}
        
        # 处理功能性能指标
        for bucket in response.aggregations.features.buckets:
            url = bucket.key
            # 尝试匹配功能名称
            feature_name = None
            for pattern, name in feature_mappings.items():
                if pattern in url:
                    feature_name = name
                    break
            
            result['features'].append({
                'url': url,
                'feature_name': feature_name or '未映射功能',
                'request_count': bucket.doc_count,
                'avg_response_time': round(bucket.avg_time.value, 2) if hasattr(bucket, 'avg_time') else None,
                'max_response_time': round(bucket.max_time.value, 2) if hasattr(bucket, 'max_time') else None,
                'min_response_time': round(bucket.min_time.value, 2) if hasattr(bucket, 'min_time') else None,
                'percentiles': {
                    'p50': round(bucket.percentiles.values['50.0'], 2),
                    'p75': round(bucket.percentiles.values['75.0'], 2),
                    'p90': round(bucket.percentiles.values['90.0'], 2),
                    'p95': round(bucket.percentiles.values['95.0'], 2),
                    'p99': round(bucket.percentiles.values['99.0'], 2)
                }
            })
        
        return Response(result)
    
    def get_feature_trend(self, request, feature_id=None):
        """
        获取功能使用趋势
        分析特定功能随时间的使用变化趋势
        """
        # 检查功能ID
        if not feature_id:
            return Response({"error": "功能ID不能为空"}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            feature = FeatureMapping.objects.get(id=feature_id)
        except FeatureMapping.DoesNotExist:
            return Response({"error": "功能不存在"}, status=status.HTTP_404_NOT_FOUND)
        
        # 获取参数
        time_dimension = request.query_params.get('dimension', 'daily')
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        
        # 获取日期范围
        start_date, end_date = get_date_range(time_dimension, start_date, end_date)
        
        # 构建ES查询
        s = Search(using=es_client, index=ElasticsearchIndices.OPERATION_LOG_WILDCARD) \
            .filter('range', requestTime={'gte': start_date, 'lte': end_date}) \
            .filter('wildcard', requestUrl=f"*{feature.url_pattern}*")
        
        # 按时间维度聚合
        if time_dimension == 'daily':
            s.aggs.bucket('trend', 'date_histogram', field='requestTime', calendar_interval='day')
        elif time_dimension == 'weekly':
            s.aggs.bucket('trend', 'date_histogram', field='requestTime', calendar_interval='week')
        elif time_dimension == 'monthly':
            s.aggs.bucket('trend', 'date_histogram', field='requestTime', calendar_interval='month')
        
        # 执行查询
        response = s.execute()
        
        # 处理结果
        result = {
            'feature_id': feature_id,
            'feature_name': feature.feature_name,
            'url_pattern': feature.url_pattern,
            'date_range': {
                'start': start_date.isoformat(),
                'end': end_date.isoformat(),
            },
            'time_dimension': time_dimension,
            'trend': []
        }
        
        # 处理趋势数据
        for bucket in response.aggregations.trend.buckets:
            result['trend'].append({
                'time': bucket.key_as_string,
                'count': bucket.doc_count
            })
        
        return Response(result)
    
    def get_feature_quality(self, request):
        """
        获取功能质量分析
        分析功能错误率、错误类型分布等
        """
        # 获取参数
        time_dimension = request.query_params.get('dimension', 'daily')
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        top_n = int(request.query_params.get('top_n', 10))
        
        # 获取日期范围
        start_date, end_date = get_date_range(time_dimension, start_date, end_date)
        
        # 构建ES查询 - 总请求
        s_total = Search(using=es_client, index=ElasticsearchIndices.OPERATION_LOG_WILDCARD) \
            .filter('range', requestTime={'gte': start_date, 'lte': end_date})
        
        # 按URL聚合
        s_total.aggs.bucket('features', 'terms', field='requestUrl.keyword', size=top_n)
        
        # 构建ES查询 - 错误请求
        s_error = Search(using=es_client, index=ElasticsearchIndices.OPERATION_LOG_WILDCARD) \
            .filter('range', requestTime={'gte': start_date, 'lte': end_date}) \
            .filter('term', operateResult='失败')
        
        # 按URL聚合并统计错误类型
        s_error.aggs.bucket('features', 'terms', field='requestUrl.keyword', size=top_n) \
            .bucket('error_types', 'terms', field='requestResult.keyword', size=10)
        
        # 执行查询
        response_total = s_total.execute()
        response_error = s_error.execute()
        
        # 处理结果
        result = {
            'date_range': {
                'start': start_date.isoformat(),
                'end': end_date.isoformat(),
            },
            'features': []
        }
        
        # 获取功能映射
        feature_mappings = {fm.url_pattern: fm.feature_name for fm in FeatureMapping.objects.all()}
        
        # 创建错误数据字典
        error_data = {}
        for bucket in response_error.aggregations.features.buckets:
            url = bucket.key
            error_data[url] = {
                'error_count': bucket.doc_count,
                'error_types': [{
                    'type': error_bucket.key,
                    'count': error_bucket.doc_count
                } for error_bucket in bucket.error_types.buckets]
            }
        
        # 处理功能质量指标
        for bucket in response_total.aggregations.features.buckets:
            url = bucket.key
            total_count = bucket.doc_count
            
            # 获取错误数据
            url_error_data = error_data.get(url, {'error_count': 0, 'error_types': []})
            error_count = url_error_data['error_count']
            error_rate = round(error_count / total_count * 100, 2) if total_count > 0 else 0
            
            # 尝试匹配功能名称
            feature_name = None
            for pattern, name in feature_mappings.items():
                if pattern in url:
                    feature_name = name
                    break
            
            result['features'].append({
                'url': url,
                'feature_name': feature_name or '未映射功能',
                'total_count': total_count,
                'error_count': error_count,
                'error_rate': error_rate,
                'error_types': url_error_data['error_types']
            })
        
        # 按错误率排序
        result['features'] = sorted(result['features'], key=lambda x: x['error_rate'], reverse=True)
        
        return Response(result)