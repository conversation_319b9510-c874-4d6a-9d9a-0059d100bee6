# 报表模块模型

from django.db import models

class ReportConfig(models.Model):
    """
    报表配置表
    存储报表模板和配置信息
    """
    report_name = models.CharField(max_length=100, verbose_name='报表名称')
    report_type = models.CharField(max_length=50, verbose_name='报表类型')
    config_json = models.TextField(verbose_name='配置JSON')
    created_by = models.BigIntegerField(null=True, blank=True, verbose_name='创建人ID')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    is_public = models.BooleanField(default=False, verbose_name='是否公开')
    
    class Meta:
        db_table = 'report_config'
        verbose_name = '报表配置'
        verbose_name_plural = verbose_name
        
    def __str__(self):
        return self.report_name