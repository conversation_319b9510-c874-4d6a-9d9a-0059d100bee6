# 报表模块视图

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.http import HttpResponse
import json
import pandas as pd
import io

from .models import ReportConfig
from src.utils.es_client import es_client
from src.utils.date_utils import get_date_range


class ReportViewSet(viewsets.ViewSet):
    """
    报表视图集
    提供报表模板管理、报表生成和导出等功能
    """
    
    def get_templates(self, request):
        """
        获取报表模板列表
        支持按类型和创建人筛选
        """
        # 获取参数
        report_type = request.query_params.get('type')
        created_by = request.query_params.get('created_by')
        is_public = request.query_params.get('is_public', 'true').lower() == 'true'
        
        # 构建查询
        query = ReportConfig.objects.all()
        
        if report_type:
            query = query.filter(report_type=report_type)
        
        if created_by:
            query = query.filter(created_by=created_by)
        else:
            # 如果没有指定创建人，只返回公开的报表
            query = query.filter(is_public=is_public)
        
        # 执行查询
        templates = query.order_by('-created_at')
        
        # 格式化结果
        result = [{
            'id': template.id,
            'report_name': template.report_name,
            'report_type': template.report_type,
            'created_by': template.created_by,
            'created_at': template.created_at.isoformat(),
            'is_public': template.is_public,
            'config': json.loads(template.config_json)
        } for template in templates]
        
        return Response(result)
    
    def generate_report(self, request):
        """
        生成自定义报表
        根据提供的配置生成报表数据
        """
        # 获取报表配置
        template_id = request.data.get('template_id')
        custom_config = request.data.get('config')
        
        if template_id:
            # 使用模板生成报表
            try:
                template = ReportConfig.objects.get(id=template_id)
                config = json.loads(template.config_json)
                # 合并自定义配置
                if custom_config:
                    config.update(custom_config)
            except ReportConfig.DoesNotExist:
                return Response({"error": "报表模板不存在"}, status=status.HTTP_404_NOT_FOUND)
        elif custom_config:
            # 使用自定义配置生成报表
            config = custom_config
        else:
            return Response({"error": "需要提供模板ID或自定义配置"}, status=status.HTTP_400_BAD_REQUEST)
        
        # 根据配置生成报表数据
        report_type = config.get('report_type')
        data_source = config.get('data_source')
        dimensions = config.get('dimensions', [])
        metrics = config.get('metrics', [])
        filters = config.get('filters', {})
        time_range = config.get('time_range', {})
        
        # 根据报表类型和数据源获取数据
        if data_source == 'elasticsearch':
            # 从ES获取数据
            report_data = self._get_data_from_es(report_type, dimensions, metrics, filters, time_range)
        else:
            # 从MySQL获取数据
            report_data = self._get_data_from_db(report_type, dimensions, metrics, filters, time_range)
        
        # 构建结果
        result = {
            'config': config,
            'data': report_data
        }
        
        return Response(result)
    
    def export_report(self, request):
        """
        导出报表
        支持导出为Excel或PDF格式
        """
        # 获取参数
        report_data = request.data.get('data')
        export_type = request.query_params.get('type', 'excel')
        
        if not report_data:
            return Response({"error": "报表数据不能为空"}, status=status.HTTP_400_BAD_REQUEST)
        
        if export_type == 'excel':
            # 导出为Excel
            return self._export_excel(report_data)
        elif export_type == 'pdf':
            # 导出为PDF
            return self._export_pdf(report_data)
        else:
            return Response({"error": "不支持的导出类型"}, status=status.HTTP_400_BAD_REQUEST)
    
    def _get_data_from_es(self, report_type, dimensions, metrics, filters, time_range):
        """
        从Elasticsearch获取报表数据
        """
        # 获取时间范围
        start_date = time_range.get('start_date')
        end_date = time_range.get('end_date')
        time_dimension = time_range.get('dimension', 'daily')
        
        start_date, end_date = get_date_range(time_dimension, start_date, end_date)
        
        # 构建ES查询
        from elasticsearch_dsl import Search, Q
        s = Search(using=es_client, index="tgtweb_operatelog-*") \
            .filter('range', requestTime={'gte': start_date, 'lte': end_date})
        
        # 添加过滤条件
        for field, value in filters.items():
            if isinstance(value, list):
                s = s.filter('terms', **{field: value})
            else:
                s = s.filter('term', **{field: value})
        
        # 根据报表类型构建不同的聚合查询
        if report_type == 'time_series':
            # 时间序列报表
            if time_dimension == 'daily':
                s.aggs.bucket('by_time', 'date_histogram', field='requestTime', calendar_interval='day')
            elif time_dimension == 'weekly':
                s.aggs.bucket('by_time', 'date_histogram', field='requestTime', calendar_interval='week')
            elif time_dimension == 'monthly':
                s.aggs.bucket('by_time', 'date_histogram', field='requestTime', calendar_interval='month')
            
            # 添加维度聚合
            if dimensions:
                agg = s.aggs['by_time']
                for dimension in dimensions:
                    agg = agg.bucket(f'by_{dimension}', 'terms', field=self._get_es_field(dimension), size=10)
            
            # 添加指标聚合
            if metrics:
                agg = s.aggs['by_time']
                if dimensions:
                    for dimension in dimensions:
                        agg = agg[f'by_{dimension}']
                
                for metric in metrics:
                    if metric == 'count':
                        pass  # 默认就有doc_count
                    elif metric == 'avg_time':
                        agg.metric(metric, 'avg', field='costTime')
                    elif metric == 'max_time':
                        agg.metric(metric, 'max', field='costTime')
        
        elif report_type == 'distribution':
            # 分布报表
            for dimension in dimensions:
                s.aggs.bucket(f'by_{dimension}', 'terms', field=self._get_es_field(dimension), size=20)
        
        # 执行查询
        response = s.execute()
        
        # 处理结果
        result = self._process_es_response(response, report_type, dimensions, metrics, time_dimension)
        
        return result
    
    def _get_data_from_db(self, report_type, dimensions, metrics, filters, time_range):
        """
        从MySQL数据库获取报表数据
        """
        # 根据报表类型从不同的统计表获取数据
        time_dimension = time_range.get('dimension', 'daily')
        
        if time_dimension == 'daily':
            from src.apps.analytics.models import DailyStatistics
            stats_model = DailyStatistics
            date_field = 'stat_date'
        elif time_dimension == 'weekly':
            from src.apps.analytics.models import WeeklyStatistics
            stats_model = WeeklyStatistics
            date_field = 'year_week'
        elif time_dimension == 'monthly':
            from src.apps.analytics.models import MonthlyStatistics
            stats_model = MonthlyStatistics
            date_field = 'year_month'
        else:
            return []
        
        # 构建查询
        query = stats_model.objects.all()
        
        # 添加时间范围过滤
        start_date = time_range.get('start_date')
        end_date = time_range.get('end_date')
        
        if start_date:
            query = query.filter(**{f'{date_field}__gte': start_date})
        if end_date:
            query = query.filter(**{f'{date_field}__lte': end_date})
        
        # 添加维度过滤
        if dimensions:
            dimension = dimensions[0]  # 目前只支持一个维度
            query = query.filter(dimension=dimension)
        
        # 添加指标过滤
        if metrics:
            metric = metrics[0]  # 目前只支持一个指标
            query = query.filter(metric_name=metric)
        
        # 添加其他过滤条件
        for field, value in filters.items():
            if field in ['dimension_value']:
                if isinstance(value, list):
                    query = query.filter(**{f'{field}__in': value})
                else:
                    query = query.filter(**{field: value})
        
        # 执行查询
        stats = query.order_by(date_field, 'dimension_value')
        
        # 处理结果
        result = []
        for stat in stats:
            if time_dimension == 'daily':
                time_value = stat.stat_date.isoformat()
            else:
                time_value = getattr(stat, date_field)
            
            result.append({
                'time': time_value,
                'dimension': stat.dimension,
                'dimension_value': stat.dimension_value,
                'metric': stat.metric_name,
                'value': stat.metric_value
            })
        
        return result
    
    def _process_es_response(self, response, report_type, dimensions, metrics, time_dimension):
        """
        处理ES响应结果
        """
        result = []
        
        if report_type == 'time_series':
            # 处理时间序列报表
            for time_bucket in response.aggregations.by_time.buckets:
                time_value = time_bucket.key_as_string
                if time_dimension == 'daily':
                    time_value = time_value[:10]  # 只取日期部分
                
                if not dimensions:
                    # 没有维度，直接添加时间和指标
                    item = {'time': time_value}
                    
                    for metric in metrics:
                        if metric == 'count':
                            item[metric] = time_bucket.doc_count
                        else:
                            item[metric] = getattr(time_bucket, metric).value
                    
                    result.append(item)
                else:
                    # 有维度，需要递归处理
                    self._process_dimension_buckets(result, time_bucket, dimensions, metrics, 0, {'time': time_value})
        
        elif report_type == 'distribution':
            # 处理分布报表
            for dimension in dimensions:
                dim_agg = getattr(response.aggregations, f'by_{dimension}')
                for bucket in dim_agg.buckets:
                    result.append({
                        'dimension': dimension,
                        'value': bucket.key,
                        'count': bucket.doc_count
                    })
        
        return result
    
    def _process_dimension_buckets(self, result, parent_bucket, dimensions, metrics, dim_index, current_item):
        """
        递归处理维度聚合桶
        """
        if dim_index >= len(dimensions):
            # 已经处理完所有维度，添加指标
            for metric in metrics:
                if metric == 'count':
                    current_item[metric] = parent_bucket.doc_count
                else:
                    current_item[metric] = getattr(parent_bucket, metric).value
            
            result.append(current_item.copy())
            return
        
        # 获取当前维度
        dimension = dimensions[dim_index]
        dim_agg = getattr(parent_bucket, f'by_{dimension}')
        
        for bucket in dim_agg.buckets:
            # 添加当前维度值
            current_item[dimension] = bucket.key
            
            # 递归处理下一个维度
            self._process_dimension_buckets(result, bucket, dimensions, metrics, dim_index + 1, current_item)
    
    def _export_excel(self, report_data):
        """
        导出为Excel格式
        """
        # 创建DataFrame
        df = pd.DataFrame(report_data)
        
        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='Report', index=False)
        
        # 设置响应头
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename=report.xlsx'
        
        return response
    
    def _export_pdf(self, report_data):
        """
        导出为PDF格式
        注意：实际实现需要依赖PDF生成库，这里只是示例
        """
        # 创建DataFrame
        df = pd.DataFrame(report_data)
        
        # 创建HTML
        html = df.to_html(index=False)
        
        # 这里应该使用PDF生成库将HTML转换为PDF
        # 例如：pdfkit, weasyprint等
        # 这里简化处理，直接返回HTML
        
        response = HttpResponse(html, content_type='text/html')
        response['Content-Disposition'] = 'attachment; filename=report.html'
        
        return response
    
    def _get_es_field(self, dimension):
        """
        根据维度名称获取对应的ES字段
        """
        dimension_mapping = {
            'user': 'operateUserId',
            'feature': 'requestUrl.keyword',
            'ip': 'ip.keyword',
            'result': 'operateResult.keyword'
        }
        return dimension_mapping.get(dimension, dimension)