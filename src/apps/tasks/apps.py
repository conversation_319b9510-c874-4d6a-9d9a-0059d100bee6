from django.apps import AppConfig


class TasksConfig(AppConfig):
    # Django 2.2不支持default_auto_field设置
    # default_auto_field = 'django.db.models.BigAutoField'
    name = 'src.apps.tasks'

    verbose_name = '定时任务'

    def ready(self):
        """
        在Django应用启动时执行
        导入必要模块并启动定时任务调度器
        """
        # 导入必要的模块
        from .models import ScheduledTask, TaskExecutionLog
        from .scheduler import scheduler, start_scheduler, shutdown_scheduler
        from .tasks import (
            daily_aggregation_task,
            weekly_aggregation_task,
            monthly_aggregation_task,
            quarterly_aggregation_task,
            user_behavior_analysis_task  # 添加用户行为分析任务
        )

        # 启动调度器
        # 避免在Django测试或迁移命令中启动调度器
        import sys
        import os

        # 检查是否是主进程
        if os.environ.get('RUN_MAIN') != 'true':  # 避免在Django自动重载进程中启动
            # 排除测试和迁移命令
            if not any(cmd in sys.argv for cmd in ['test', 'makemigrations', 'migrate', 'shell']):
                print("跳过自动启动调度器，将使用单独的简单调度器")
                # 禁用自动启动调度器，使用单独的简单调度器
                # start_scheduler()