import json
import argparse
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone

from src.apps.tasks.models import ScheduledTask, TaskExecutionLog
from src.apps.tasks.scheduler import scheduler


class Command(BaseCommand):
    help = '管理定时任务的命令行工具'

    def add_arguments(self, parser):
        subparsers = parser.add_subparsers(dest='command', help='子命令')
        
        # 列出所有任务
        list_parser = subparsers.add_parser('list', help='列出所有任务')
        list_parser.add_argument('--active', action='store_true', help='只显示活跃的任务')
        list_parser.add_argument('--type', type=str, help='按任务类型筛选')
        
        # 创建新任务
        create_parser = subparsers.add_parser('create', help='创建新任务')
        create_parser.add_argument('--name', type=str, required=True, help='任务名称')
        create_parser.add_argument('--type', type=str, required=True, help='任务类型')
        create_parser.add_argument('--cron', type=str, required=True, help='Cron表达式')
        create_parser.add_argument('--params', type=str, help='任务参数(JSON格式)')
        create_parser.add_argument('--active', action='store_true', help='是否激活任务')
        
        # 更新任务
        update_parser = subparsers.add_parser('update', help='更新任务')
        update_parser.add_argument('task_id', type=int, help='任务ID')
        update_parser.add_argument('--name', type=str, help='任务名称')
        update_parser.add_argument('--type', type=str, help='任务类型')
        update_parser.add_argument('--cron', type=str, help='Cron表达式')
        update_parser.add_argument('--params', type=str, help='任务参数(JSON格式)')
        update_parser.add_argument('--active', type=bool, help='是否激活任务')
        
        # 删除任务
        delete_parser = subparsers.add_parser('delete', help='删除任务')
        delete_parser.add_argument('task_id', type=int, help='任务ID')
        
        # 执行任务
        execute_parser = subparsers.add_parser('execute', help='立即执行任务')
        execute_parser.add_argument('task_id', type=int, help='任务ID')
        
        # 查看任务日志
        logs_parser = subparsers.add_parser('logs', help='查看任务执行日志')
        logs_parser.add_argument('task_id', type=int, help='任务ID')
        logs_parser.add_argument('--limit', type=int, default=10, help='显示日志数量')
        
        # 重新加载所有任务
        subparsers.add_parser('reload', help='重新加载所有活跃任务')

    def handle(self, *args, **options):
        command = options['command']
        
        if command == 'list':
            self.list_tasks(options)
        elif command == 'create':
            self.create_task(options)
        elif command == 'update':
            self.update_task(options)
        elif command == 'delete':
            self.delete_task(options)
        elif command == 'execute':
            self.execute_task(options)
        elif command == 'logs':
            self.show_logs(options)
        elif command == 'reload':
            self.reload_tasks()
        else:
            raise CommandError('未知的命令: %s' % command)

    def list_tasks(self, options):
        """列出所有任务"""
        tasks = ScheduledTask.objects.all()
        
        if options['active']:
            tasks = tasks.filter(is_active=True)
        
        if options['type']:
            tasks = tasks.filter(task_type=options['type'])
        
        if not tasks:
            self.stdout.write(self.style.WARNING('没有找到任务'))
            return
        
        self.stdout.write(self.style.SUCCESS('任务列表:'))
        for task in tasks:
            status = '活跃' if task.is_active else '非活跃'
            self.stdout.write(f"ID: {task.id}, 名称: {task.task_name}, 类型: {task.task_type}, 状态: {status}")
            self.stdout.write(f"  Cron表达式: {task.cron_expression}")
            self.stdout.write(f"  上次运行: {task.last_run_time or '从未运行'}")
            self.stdout.write(f"  下次运行: {task.next_run_time or '未调度'}")
            self.stdout.write('')

    def create_task(self, options):
        """创建新任务"""
        try:
            task_params = None
            if options['params']:
                task_params = json.loads(options['params'])
            
            task = ScheduledTask.objects.create(
                task_name=options['name'],
                task_type=options['type'],
                cron_expression=options['cron'],
                task_params=json.dumps(task_params) if task_params else None,
                is_active=options['active']
            )
            
            if options['active']:
                scheduler.add_task(task)
            
            self.stdout.write(self.style.SUCCESS(f"任务创建成功，ID: {task.id}"))
        
        except Exception as e:
            raise CommandError(f"任务创建失败: {str(e)}")

    def update_task(self, options):
        """更新任务"""
        try:
            task_id = options['task_id']
            task = ScheduledTask.objects.get(pk=task_id)
            
            # 更新任务字段
            if options['name']:
                task.task_name = options['name']
            
            if options['type']:
                task.task_type = options['type']
            
            if options['cron']:
                task.cron_expression = options['cron']
            
            if options['params']:
                task_params = json.loads(options['params'])
                task.task_params = json.dumps(task_params)
            
            if options['active'] is not None:
                old_is_active = task.is_active
                task.is_active = options['active']
                
                # 处理活跃状态变更
                if not old_is_active and options['active']:
                    scheduler.add_task(task)
                elif old_is_active and not options['active']:
                    scheduler.remove_task(task.id)
            
            task.save()
            
            # 如果任务保持活跃状态但配置发生变化，需要更新调度器中的任务
            if task.is_active:
                scheduler.remove_task(task.id)
                scheduler.add_task(task)
            
            self.stdout.write(self.style.SUCCESS(f"任务更新成功，ID: {task.id}"))
        
        except ScheduledTask.DoesNotExist:
            raise CommandError(f"任务不存在: {task_id}")
        except Exception as e:
            raise CommandError(f"任务更新失败: {str(e)}")

    def delete_task(self, options):
        """删除任务"""
        try:
            task_id = options['task_id']
            task = ScheduledTask.objects.get(pk=task_id)
            
            # 如果任务是活跃的，先从调度器中移除
            if task.is_active:
                scheduler.remove_task(task.id)
            
            task_name = task.task_name
            task.delete()
            
            self.stdout.write(self.style.SUCCESS(f"任务删除成功: {task_name}"))
        
        except ScheduledTask.DoesNotExist:
            raise CommandError(f"任务不存在: {task_id}")
        except Exception as e:
            raise CommandError(f"任务删除失败: {str(e)}")

    def execute_task(self, options):
        """立即执行任务"""
        try:
            task_id = options['task_id']
            task = ScheduledTask.objects.get(pk=task_id)
            
            self.stdout.write(f"正在执行任务: {task.task_name}...")
            result = scheduler.execute_task_now(task.id)
            
            if 'error' in result:
                self.stdout.write(self.style.ERROR(f"任务执行失败: {result['error']}"))
            else:
                self.stdout.write(self.style.SUCCESS("任务执行成功"))
                self.stdout.write(f"结果: {json.dumps(result, indent=2)}")
        
        except ScheduledTask.DoesNotExist:
            raise CommandError(f"任务不存在: {task_id}")
        except Exception as e:
            raise CommandError(f"任务执行失败: {str(e)}")

    def show_logs(self, options):
        """查看任务执行日志"""
        try:
            task_id = options['task_id']
            limit = options['limit']
            
            task = ScheduledTask.objects.get(pk=task_id)
            logs = TaskExecutionLog.objects.filter(task=task).order_by('-start_time')[:limit]
            
            if not logs:
                self.stdout.write(self.style.WARNING(f"任务 {task.task_name} 没有执行日志"))
                return
            
            self.stdout.write(self.style.SUCCESS(f"任务 {task.task_name} 的执行日志:"))
            for log in logs:
                status_style = self.style.SUCCESS if log.status == 'success' else self.style.ERROR
                self.stdout.write(f"ID: {log.id}, 开始时间: {log.start_time}, 状态: {status_style(log.status)}")
                
                if log.end_time:
                    duration = (log.end_time - log.start_time).total_seconds()
                    self.stdout.write(f"  结束时间: {log.end_time}, 耗时: {duration:.2f}秒")
                
                if log.affected_rows:
                    self.stdout.write(f"  影响行数: {log.affected_rows}")
                
                if log.error_message:
                    self.stdout.write(f"  错误信息: {log.error_message}")
                
                self.stdout.write('')
        
        except ScheduledTask.DoesNotExist:
            raise CommandError(f"任务不存在: {task_id}")
        except Exception as e:
            raise CommandError(f"获取日志失败: {str(e)}")

    def reload_tasks(self):
        """重新加载所有活跃任务"""
        try:
            scheduler.reload_tasks()
            self.stdout.write(self.style.SUCCESS("所有活跃任务已重新加载"))
        except Exception as e:
            raise CommandError(f"重新加载任务失败: {str(e)}")