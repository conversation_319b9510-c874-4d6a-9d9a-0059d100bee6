# Generated by Django 3.2.18 on 2025-04-17 23:59

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ScheduledTask',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_name', models.CharField(max_length=100, verbose_name='任务名称')),
                ('task_type', models.CharField(choices=[('daily', '每日任务'), ('weekly', '每周任务'), ('monthly', '每月任务'), ('quarterly', '每季度任务'), ('yearly', '每年任务')], max_length=50, verbose_name='任务类型')),
                ('cron_expression', models.CharField(max_length=100, verbose_name='Cron表达式')),
                ('task_params', models.TextField(blank=True, help_text='JSON格式的任务参数', null=True, verbose_name='任务参数')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('last_run_time', models.DateTimeField(blank=True, null=True, verbose_name='上次运行时间')),
                ('next_run_time', models.DateTimeField(blank=True, null=True, verbose_name='下次运行时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '定时任务',
                'verbose_name_plural': '定时任务',
                'db_table': 'scheduled_task',
            },
        ),
        migrations.CreateModel(
            name='TaskExecutionLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_time', models.DateTimeField(verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
                ('status', models.CharField(choices=[('running', '运行中'), ('success', '成功'), ('failed', '失败')], max_length=20, verbose_name='状态')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='错误信息')),
                ('affected_rows', models.IntegerField(default=0, verbose_name='影响行数')),
                ('execution_details', models.TextField(blank=True, null=True, verbose_name='执行详情')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='tasks.scheduledtask', verbose_name='关联任务')),
            ],
            options={
                'verbose_name': '任务执行日志',
                'verbose_name_plural': '任务执行日志',
                'db_table': 'task_execution_log',
            },
        ),
    ]
