from django.db import models

class ScheduledTask(models.Model):
    """
    定时任务配置表
    用于存储系统中的定时任务配置信息
    """
    TASK_TYPE_CHOICES = [
        ('daily', '每日任务'),
        ('weekly', '每周任务'),
        ('monthly', '每月任务'),
        ('quarterly', '每季度任务'),
        ('yearly', '每年任务'),
    ]
    
    task_name = models.CharField(max_length=100, verbose_name='任务名称')
    task_type = models.CharField(max_length=50, choices=TASK_TYPE_CHOICES, verbose_name='任务类型')
    cron_expression = models.CharField(max_length=100, verbose_name='Cron表达式')
    task_params = models.TextField(null=True, blank=True, verbose_name='任务参数', help_text='JSON格式的任务参数')
    is_active = models.BooleanField(default=True, verbose_name='是否激活')
    last_run_time = models.DateTimeField(null=True, blank=True, verbose_name='上次运行时间')
    next_run_time = models.DateTimeField(null=True, blank=True, verbose_name='下次运行时间')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'scheduled_task'
        verbose_name = '定时任务'
        verbose_name_plural = '定时任务'
        
    def __str__(self):
        return self.task_name


class TaskExecutionLog(models.Model):
    """
    定时任务执行日志表
    用于记录定时任务的执行情况
    """
    STATUS_CHOICES = [
        ('running', '运行中'),
        ('success', '成功'),
        ('failed', '失败'),
    ]
    
    task = models.ForeignKey(ScheduledTask, on_delete=models.CASCADE, verbose_name='关联任务')
    start_time = models.DateTimeField(verbose_name='开始时间')
    end_time = models.DateTimeField(null=True, blank=True, verbose_name='结束时间')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, verbose_name='状态')
    error_message = models.TextField(null=True, blank=True, verbose_name='错误信息')
    affected_rows = models.IntegerField(default=0, verbose_name='影响行数')
    execution_details = models.TextField(null=True, blank=True, verbose_name='执行详情')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        db_table = 'task_execution_log'
        verbose_name = '任务执行日志'
        verbose_name_plural = '任务执行日志'
        
    def __str__(self):
        return f"{self.task.task_name} - {self.start_time}"