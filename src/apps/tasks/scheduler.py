import logging
import json
from datetime import datetime, timedelta
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from django.conf import settings
from django.utils import timezone
from django.db import transaction
from django.db.models import Count, Avg, F, Sum

# 延迟导入模型，不在模块级别检查应用是否准备好
from django.apps import apps
# apps.check_apps_ready() - 移除此检查，避免循环导入问题

from .models import ScheduledTask, TaskExecutionLog
from .tasks import (
    daily_aggregation_task,
    weekly_aggregation_task,
    monthly_aggregation_task,
    quarterly_aggregation_task,
    user_behavior_analysis_task
)
from src.apps.analytics.models import UserOperateAnalysisDay

logger = logging.getLogger(__name__)

class TaskScheduler:
    """
    定时任务调度器
    负责管理和执行系统中的定时任务
    """
    _instance = None
    _scheduler = None

    # 任务类型映射到具体的任务函数
    TASK_FUNCTIONS = {
        'daily': daily_aggregation_task,
        'weekly': weekly_aggregation_task,
        'monthly': monthly_aggregation_task,
        'quarterly': quarterly_aggregation_task,
        'user_behavior_analysis': user_behavior_analysis_task,
    }

    @classmethod
    def get_instance(cls):
        """
        获取调度器单例实例
        """
        if cls._instance is None:
            cls._instance = TaskScheduler()
        return cls._instance

    def __init__(self):
        """
        初始化调度器
        """
        # 使用默认配置创建BackgroundScheduler
        import pytz
        # 使用本地时间（北京时间）
        self._scheduler = BackgroundScheduler()

        # 添加事件监听器
        from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR
        self._scheduler.add_listener(self._task_listener, EVENT_JOB_EXECUTED)
        self._scheduler.add_listener(self._task_error_listener, EVENT_JOB_ERROR)

    def start(self):
        """
        启动调度器并加载所有活跃的任务
        """
        if not self._scheduler.running:
            # 先加载任务，再启动调度器
            self.load_tasks()
            self._scheduler.start()
            logger.info("任务调度器已启动")

            # 输出所有任务的信息
            jobs = self._scheduler.get_jobs()
            logger.info(f"当前调度器中的任务数量: {len(jobs)}")
            for job in jobs:
                logger.info(f"\t任务ID: {job.id}, 名称: {job.name}, 下次执行时间: {job.next_run_time}")
        else:
            logger.info("调度器已经在运行中")

    def shutdown(self):
        """
        关闭调度器
        """
        if self._scheduler.running:
            self._scheduler.shutdown()
            logger.info("任务调度器已关闭")

    def load_tasks(self):
        """
        加载所有活跃的任务
        """
        # 加载数据库中的活跃任务
        active_tasks = ScheduledTask.objects.filter(is_active=True)
        for task in active_tasks:
            self.add_task(task)

        logger.info(f"已加载 {active_tasks.count()} 个活跃任务")

    def reload_tasks(self):
        """
        重新加载所有活跃的任务
        """
        # 清除所有现有任务
        self._scheduler.remove_all_jobs()

        # 加载数据库中的活跃任务
        self.load_tasks()

    def add_task(self, task):
        """
        添加任务到调度器

        参数:
            task: ScheduledTask 实例
        """
        if task.task_type not in self.TASK_FUNCTIONS:
            logger.error(f"未知的任务类型: {task.task_type}")
            return

        # 解析任务参数
        task_params = {}
        if task.task_params:
            try:
                task_params = json.loads(task.task_params)
            except json.JSONDecodeError:
                logger.error(f"任务参数解析失败: {task.task_params}")

        # 创建任务执行函数
        def execute_task():
            return self._execute_task(task.id, task_params)

        # 添加到调度器
        import pytz
        job = self._scheduler.add_job(
            execute_task,
            CronTrigger.from_crontab(task.cron_expression),
            id=f"task_{task.id}",
            name=task.task_name,
            replace_existing=True
        )

        # 更新下次运行时间
        if hasattr(job, 'next_run_time') and job.next_run_time:
            next_run_time = job.next_run_time
            task.next_run_time = next_run_time
            task.save(update_fields=['next_run_time'])
            logger.info(f"已添加任务: {task.task_name}, 下次运行时间: {next_run_time}")
        else:
            logger.info(f"已添加任务: {task.task_name}, 但无法获取下次运行时间")

    def remove_task(self, task_id):
        """
        从调度器中移除任务

        参数:
            task_id: 任务ID
        """
        job_id = f"task_{task_id}"
        try:
            self._scheduler.remove_job(job_id)
            logger.info(f"已移除任务: {job_id}")
        except Exception as e:
            logger.error(f"移除任务失败: {job_id}, 错误: {str(e)}")

    def execute_task_now(self, task_id):
        """
        立即执行指定任务

        参数:
            task_id: 任务ID

        返回:
            dict: 任务执行结果
        """
        try:
            task = ScheduledTask.objects.get(id=task_id)
            task_params = {}
            if task.task_params:
                try:
                    task_params = json.loads(task.task_params)
                except json.JSONDecodeError:
                    logger.error(f"任务参数解析失败: {task.task_params}")

            return self._execute_task(task_id, task_params)
        except ScheduledTask.DoesNotExist:
            logger.error(f"任务不存在: {task_id}")
            return {"error": "任务不存在"}

    def _execute_task(self, task_id, task_params):
        """
        执行任务并记录执行日志

        参数:
            task_id: 任务ID
            task_params: 任务参数

        返回:
            dict: 任务执行结果
        """
        task = ScheduledTask.objects.get(id=task_id)
        task_function = self.TASK_FUNCTIONS.get(task.task_type)

        if not task_function:
            logger.error(f"未找到任务函数: {task.task_type}")
            return {"error": f"未找到任务函数: {task.task_type}"}

        # 创建任务执行日志
        start_time = timezone.now()
        log = TaskExecutionLog.objects.create(
            task_id=task_id,
            start_time=start_time,
            status='running'
        )

        try:
            # 执行任务
            result = task_function(task_params)

            # 更新任务执行日志
            end_time = timezone.now()
            with transaction.atomic():
                log.end_time = end_time
                log.status = 'success'
                log.affected_rows = result.get('affected_rows', 0)
                log.execution_details = json.dumps(result)
                log.save()

                # 更新任务的最后运行时间
                task.last_run_time = start_time
                task.save(update_fields=['last_run_time'])

            logger.info(f"任务执行成功: {task.task_name}, 耗时: {(end_time - start_time).total_seconds()}秒")
            return result

        except Exception as e:
            # 更新任务执行日志为失败状态
            end_time = timezone.now()
            log.end_time = end_time
            log.status = 'failed'
            log.error_message = str(e)
            log.save()

            logger.error(f"任务执行失败: {task.task_name}, 错误: {str(e)}")
            return {"error": str(e)}

    def _task_listener(self, event):
        """
        任务执行完成监听器
        """
        job_id = event.job_id
        if job_id and job_id.startswith('task_'):
            task_id = int(job_id.split('_')[1])
            try:
                task = ScheduledTask.objects.get(id=task_id)
                # 更新下次运行时间和上次运行时间
                from django.utils import timezone
                now = timezone.now()
                task.last_run_time = now

                # 获取下次运行时间
                job = self._scheduler.get_job(job_id)
                if job and job.next_run_time:
                    task.next_run_time = job.next_run_time

                task.save(update_fields=['last_run_time', 'next_run_time'])
                logger.info(f"任务执行成功: {task.task_name}, 下次执行时间: {task.next_run_time}")
            except ScheduledTask.DoesNotExist:
                logger.warning(f"任务不存在: {task_id}")

    def _task_error_listener(self, event):
        """
        任务执行错误监听器
        """
        job_id = event.job_id
        if job_id and job_id.startswith('task_'):
            task_id = int(job_id.split('_')[1])
            logger.error(f"任务执行出错: {task_id}, 错误: {str(event.exception)}")


# 全局调度器实例
scheduler = TaskScheduler.get_instance()


def start_scheduler():
    """
    启动任务调度器
    在Django应用启动时调用
    """
    import logging
    logger = logging.getLogger('django')

    try:
        # 检查调度器是否已经运行
        if scheduler._scheduler.running:
            logger.info("调度器已经在运行中")
            return

        # 启动调度器
        scheduler.start()
        logger.info("调度器启动成功")

        # 输出所有任务的信息
        jobs = scheduler._scheduler.get_jobs()
        logger.info(f"当前调度器中的任务数量: {len(jobs)}")
        for job in jobs:
            logger.info(f"\t任务ID: {job.id}, 名称: {job.name}, 下次执行时间: {job.next_run_time}")
    except Exception as e:
        logger.error(f"启动调度器失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())


def shutdown_scheduler():
    """
    关闭任务调度器
    在Django应用关闭时调用
    """
    scheduler.shutdown()


def get_feature_usage_stats(date_str=None, system_type='api'):
    """
    获取某一天功能使用情况统计
    返回点击率最高/最低和用户操作最多/最少的功能列表

    参数:
        date_str: 日期字符串，格式为YYYY-MM-DD，如果为None则使用昨天的日期
        system_type: 系统类型，可选值为api(默认)、oss、boss

    返回:
        dict: 包含高频/低频功能使用统计的字典
    """
    try:
        # 处理日期参数
        if date_str:
            try:
                target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                return {
                    "error": f"无效的日期格式: {date_str}. 正确格式为: YYYY-MM-DD"
                }
        else:
            # 默认使用昨天的日期
            target_date = (timezone.now() - timedelta(days=1)).date()

        logger.info(f"获取 {target_date} 的功能使用情况统计")

        # 查询指定日期和系统类型的数据
        queryset = UserOperateAnalysisDay.objects.filter(day=target_date, system_type=system_type)

        # 检查是否有数据
        if not queryset.exists():
            return {
                "error": f"日期 {target_date} 系统类型 {system_type} 没有数据",
                "date": target_date.strftime('%Y-%m-%d'),
                "system_type": system_type
            }

        # 获取点击率最高的10个功能
        # 先按URL分组聚合，避免重复
        top_clicks_agg = queryset.values('request_url') \
            .annotate(total_count=Sum('request_count'),
                     avg_response_time=Avg('avg_time')) \
            .order_by('-total_count')[:10]

        # 获取每个URL的名称（使用第一个匹配的记录的名称）
        top_clicks_data = []
        for item in top_clicks_agg:
            # 获取该URL的第一条记录的URL名称
            url_name = queryset.filter(request_url=item['request_url']).values_list('request_url_name', flat=True).first() or item['request_url']
            top_clicks_data.append({
                "request_url": item['request_url'],
                "request_url_name": url_name,
                "request_count": item['total_count'],
                "avg_time": round(item['avg_response_time'])
            })

        # 获取点击率最低的10个功能
        # 先按URL分组聚合，避免重复
        bottom_clicks_agg = queryset.values('request_url') \
            .annotate(total_count=Sum('request_count'),
                     avg_response_time=Avg('avg_time')) \
            .order_by('total_count')[:10]

        # 获取每个URL的名称（使用第一个匹配的记录的名称）
        bottom_clicks_data = []
        for item in bottom_clicks_agg:
            # 获取该URL的第一条记录的URL名称
            url_name = queryset.filter(request_url=item['request_url']).values_list('request_url_name', flat=True).first() or item['request_url']
            bottom_clicks_data.append({
                "request_url": item['request_url'],
                "request_url_name": url_name,
                "request_count": item['total_count'],
                "avg_time": round(item['avg_response_time'])
            })

        # 获取用户操作最多的10个功能
        # 按request_url分组，计算不同用户的数量
        top_operations = queryset.values('request_url', 'request_url_name') \
            .annotate(user_count=Count('request_user_id', distinct=True),
                     total_count=Sum('request_count'),
                     avg_response_time=Avg('avg_time')) \
            .order_by('-user_count')[:10]

        top_operations_data = [{
            "request_url": item['request_url'],
            "request_url_name": item['request_url_name'],
            "user_count": item['user_count'],
            "total_count": item['total_count'],
            "avg_response_time": item['avg_response_time']
        } for item in top_operations]

        # 获取用户操作最少的10个功能
        bottom_operations = queryset.values('request_url', 'request_url_name') \
            .annotate(user_count=Count('request_user_id', distinct=True),
                     total_count=Sum('request_count'),
                     avg_response_time=Avg('avg_time')) \
            .order_by('user_count')[:10]

        bottom_operations_data = [{
            "request_url": item['request_url'],
            "request_url_name": item['request_url_name'],
            "user_count": item['user_count'],
            "total_count": item['total_count'],
            "avg_response_time": item['avg_response_time']
        } for item in bottom_operations]

        # 返回结果
        return {
            "date": target_date.strftime('%Y-%m-%d'),
            "system_type": system_type,
            "top_clicks": top_clicks_data,
            "bottom_clicks": bottom_clicks_data,
            "top_operations": top_operations_data,
            "bottom_operations": bottom_operations_data
        }

    except Exception as e:
        logger.error(f"获取功能使用情况统计失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return {"error": f"获取功能使用情况统计失败: {str(e)}"}
