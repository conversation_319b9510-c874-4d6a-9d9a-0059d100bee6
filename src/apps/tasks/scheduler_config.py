#!/usr/bin/env python
# APScheduler配置

import logging
import pytz
from datetime import datetime
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from django.conf import settings

# 配置日志
logger = logging.getLogger(__name__)

# 创建调度器实例
scheduler = None

def get_scheduler():
    """获取调度器实例（单例模式）"""
    global scheduler
    if scheduler is None:
        # 创建调度器，使用Asia/Shanghai时区
        scheduler = BackgroundScheduler(timezone=pytz.timezone('Asia/Shanghai'))
        
        # 添加事件监听器
        from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR
        
        def job_listener(event):
            """任务执行监听器"""
            if hasattr(event, 'job_id') and event.job_id:
                if event.exception:
                    logger.error(f"任务 {event.job_id} 执行失败: {event.exception}")
                    if event.traceback:
                        logger.error(event.traceback)
                else:
                    logger.info(f"任务 {event.job_id} 执行成功")
        
        scheduler.add_listener(job_listener, EVENT_JOB_EXECUTED | EVENT_JOB_ERROR)
    
    return scheduler

def start_scheduler():
    """启动调度器"""
    scheduler = get_scheduler()
    try:
        scheduler.start()
        logger.info("调度器已启动")
        return True
    except Exception as e:
        logger.error(f"启动调度器失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def shutdown_scheduler():
    """关闭调度器"""
    global scheduler
    if scheduler:
        try:
            scheduler.shutdown()
            logger.info("调度器已关闭")
            scheduler = None
            return True
        except Exception as e:
            logger.error(f"关闭调度器失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    return True

def add_job(func, cron_expression, job_id, job_name, replace_existing=True):
    """添加定时任务"""
    scheduler = get_scheduler()
    try:
        # 使用CronTrigger，指定Asia/Shanghai时区
        trigger = CronTrigger.from_crontab(cron_expression, timezone=pytz.timezone('Asia/Shanghai'))
        
        # 添加任务
        job = scheduler.add_job(
            func,
            trigger=trigger,
            id=job_id,
            name=job_name,
            replace_existing=replace_existing
        )
        
        logger.info(f"已添加任务: {job_name}, 下次运行时间: {job.next_run_time}")
        return job
    except Exception as e:
        logger.error(f"添加任务失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def remove_job(job_id):
    """移除定时任务"""
    scheduler = get_scheduler()
    try:
        scheduler.remove_job(job_id)
        logger.info(f"已移除任务: {job_id}")
        return True
    except Exception as e:
        logger.error(f"移除任务失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False
