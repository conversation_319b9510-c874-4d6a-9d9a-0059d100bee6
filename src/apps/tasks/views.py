from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
import json

from .models import ScheduledTask, TaskExecutionLog
from .scheduler import scheduler


class TaskViewSet(viewsets.ViewSet):
    """
    定时任务管理视图集
    提供定时任务的CRUD操作和任务执行功能
    """
    
    def list(self, request):
        """
        获取任务列表
        """
        tasks = ScheduledTask.objects.all()
        
        # 支持过滤活跃状态的任务
        is_active = request.query_params.get('is_active')
        if is_active is not None:
            is_active = is_active.lower() == 'true'
            tasks = tasks.filter(is_active=is_active)
        
        # 支持过滤任务类型
        task_type = request.query_params.get('task_type')
        if task_type:
            tasks = tasks.filter(task_type=task_type)
        
        # 构建响应数据
        data = [{
            'id': task.id,
            'task_name': task.task_name,
            'task_type': task.task_type,
            'cron_expression': task.cron_expression,
            'task_params': json.loads(task.task_params) if task.task_params else {},
            'is_active': task.is_active,
            'last_run_time': task.last_run_time,
            'next_run_time': task.next_run_time,
            'created_at': task.created_at,
            'updated_at': task.updated_at
        } for task in tasks]
        
        return Response(data)
    
    def retrieve(self, request, pk=None):
        """
        获取单个任务详情
        """
        task = get_object_or_404(ScheduledTask, pk=pk)
        
        data = {
            'id': task.id,
            'task_name': task.task_name,
            'task_type': task.task_type,
            'cron_expression': task.cron_expression,
            'task_params': json.loads(task.task_params) if task.task_params else {},
            'is_active': task.is_active,
            'last_run_time': task.last_run_time,
            'next_run_time': task.next_run_time,
            'created_at': task.created_at,
            'updated_at': task.updated_at
        }
        
        return Response(data)
    
    def create(self, request):
        """
        创建新任务
        """
        try:
            # 获取请求数据
            task_name = request.data.get('task_name')
            task_type = request.data.get('task_type')
            cron_expression = request.data.get('cron_expression')
            task_params = request.data.get('task_params', {})
            is_active = request.data.get('is_active', True)
            
            # 验证必填字段
            if not all([task_name, task_type, cron_expression]):
                return Response(
                    {"error": "任务名称、任务类型和Cron表达式为必填字段"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 创建任务
            task = ScheduledTask.objects.create(
                task_name=task_name,
                task_type=task_type,
                cron_expression=cron_expression,
                task_params=json.dumps(task_params) if task_params else None,
                is_active=is_active
            )
            
            # 如果任务是活跃的，添加到调度器
            if is_active:
                scheduler.add_task(task)
            
            return Response(
                {"message": "任务创建成功", "task_id": task.id},
                status=status.HTTP_201_CREATED
            )
        
        except Exception as e:
            return Response(
                {"error": f"任务创建失败: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def update(self, request, pk=None):
        """
        更新任务
        """
        task = get_object_or_404(ScheduledTask, pk=pk)
        
        try:
            # 获取请求数据
            task_name = request.data.get('task_name', task.task_name)
            task_type = request.data.get('task_type', task.task_type)
            cron_expression = request.data.get('cron_expression', task.cron_expression)
            task_params = request.data.get('task_params')
            is_active = request.data.get('is_active')
            
            # 更新任务字段
            task.task_name = task_name
            task.task_type = task_type
            task.cron_expression = cron_expression
            
            if task_params is not None:
                task.task_params = json.dumps(task_params)
            
            # 处理活跃状态变更
            if is_active is not None:
                old_is_active = task.is_active
                task.is_active = is_active
                
                # 如果状态从非活跃变为活跃，添加到调度器
                if not old_is_active and is_active:
                    scheduler.add_task(task)
                # 如果状态从活跃变为非活跃，从调度器中移除
                elif old_is_active and not is_active:
                    scheduler.remove_task(task.id)
            
            task.save()
            
            # 如果任务保持活跃状态但配置发生变化，需要更新调度器中的任务
            if task.is_active:
                scheduler.remove_task(task.id)
                scheduler.add_task(task)
            
            return Response({"message": "任务更新成功"})
        
        except Exception as e:
            return Response(
                {"error": f"任务更新失败: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def partial_update(self, request, pk=None):
        """
        部分更新任务
        """
        return self.update(request, pk)
    
    def destroy(self, request, pk=None):
        """
        删除任务
        """
        task = get_object_or_404(ScheduledTask, pk=pk)
        
        try:
            # 如果任务是活跃的，先从调度器中移除
            if task.is_active:
                scheduler.remove_task(task.id)
            
            # 删除任务
            task.delete()
            
            return Response({"message": "任务删除成功"}, status=status.HTTP_204_NO_CONTENT)
        
        except Exception as e:
            return Response(
                {"error": f"任务删除失败: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def execute(self, request, pk=None):
        """
        手动执行任务
        """
        task = get_object_or_404(ScheduledTask, pk=pk)
        
        try:
            # 执行任务
            result = scheduler.execute_task_now(task.id)
            
            return Response({
                "message": "任务执行已触发",
                "result": result
            })
        
        except Exception as e:
            return Response(
                {"error": f"任务执行失败: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['get'])
    def logs(self, request, pk=None):
        """
        获取任务执行日志
        """
        task = get_object_or_404(ScheduledTask, pk=pk)
        
        # 获取日志列表，按时间倒序排列
        logs = TaskExecutionLog.objects.filter(task=task).order_by('-start_time')
        
        # 支持分页
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 10))
        start = (page - 1) * page_size
        end = start + page_size
        
        # 构建响应数据
        data = [{
            'id': log.id,
            'start_time': log.start_time,
            'end_time': log.end_time,
            'status': log.status,
            'error_message': log.error_message,
            'affected_rows': log.affected_rows,
            'execution_details': json.loads(log.execution_details) if log.execution_details else None,
            'created_at': log.created_at
        } for log in logs[start:end]]
        
        return Response({
            'total': logs.count(),
            'page': page,
            'page_size': page_size,
            'logs': data
        })