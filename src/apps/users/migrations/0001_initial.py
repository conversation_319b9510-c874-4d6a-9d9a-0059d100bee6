# Generated by Django 3.2.18 on 2025-05-09 11:08

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserBehaviorTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.BigIntegerField(verbose_name='用户ID')),
                ('tag_name', models.CharField(max_length=50, verbose_name='标签名称')),
                ('tag_value', models.FloatField(blank=True, null=True, verbose_name='标签值')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '用户行为标签',
                'verbose_name_plural': '用户行为标签',
                'db_table': 'user_behavior_tag',
                'unique_together': {('user_id', 'tag_name')},
            },
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(max_length=150, unique=True, verbose_name='用户名')),
                ('email', models.EmailField(blank=True, max_length=255, null=True, verbose_name='邮箱')),
                ('real_name', models.CharField(blank=True, max_length=150, null=True, verbose_name='真实姓名')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='手机号')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('is_staff', models.BooleanField(default=False, verbose_name='是否为员工')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='注册时间')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='最后登录时间')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.Group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.Permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': '用户',
                'verbose_name_plural': '用户',
                'db_table': 'auth_user',
            },
        ),
    ]
