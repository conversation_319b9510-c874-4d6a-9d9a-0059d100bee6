# Generated manually

from django.db import migrations
from django.contrib.auth.hashers import make_password


def create_default_admin(apps, schema_editor):
    # Get the User model
    User = apps.get_model('users', 'User')
    
    # Check if admin user already exists
    if not User.objects.filter(username='admin').exists():
        # Create admin user
        User.objects.create(
            username='admin',
            password=make_password('tgt51848'),  # Hash the password
            email='<EMAIL>',
            real_name='管理员',
            is_active=True,
            is_staff=True,
            is_superuser=True
        )


def remove_default_admin(apps, schema_editor):
    # Get the User model
    User = apps.get_model('users', 'User')
    
    # Delete admin user if it exists
    User.objects.filter(username='admin').delete()


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(create_default_admin, remove_default_admin),
    ]
