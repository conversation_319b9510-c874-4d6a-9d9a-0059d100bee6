# Generated manually

from django.db import migrations
from django.contrib.auth.hashers import make_password


def create_default_admin(apps, schema_editor):
    # Get the User model
    User = apps.get_model('auth', 'User')
    
    # Check if admin user already exists
    if not User.objects.filter(username='admin').exists():
        # Create admin user
        User.objects.create(
            username='admin',
            password=make_password('tgt51848'),  # Hash the password
            email='<EMAIL>',
            is_active=True,
            is_staff=True,
            is_superuser=True
        )


def remove_default_admin(apps, schema_editor):
    # Get the User model
    User = apps.get_model('auth', 'User')
    
    # Delete admin user if it exists
    User.objects.filter(username='admin').delete()


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0003_add_custom_fields'),
    ]

    operations = [
        migrations.RunPython(create_default_admin, remove_default_admin),
    ]
