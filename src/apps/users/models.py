# 用户分析模块模型

from django.db import models

class UserBehaviorTag(models.Model):
    """
    用户行为标签表
    存储基于用户行为分析生成的标签
    """
    user_id = models.BigIntegerField(verbose_name='用户ID')
    tag_name = models.CharField(max_length=50, verbose_name='标签名称')
    tag_value = models.FloatField(null=True, blank=True, verbose_name='标签值')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'user_behavior_tag'
        unique_together = ('user_id', 'tag_name')
        verbose_name = '用户行为标签'
        verbose_name_plural = verbose_name

    def __str__(self):
        return f'{self.user_id}:{self.tag_name}'


# 移除自定义User模型，使用Django默认的User模型