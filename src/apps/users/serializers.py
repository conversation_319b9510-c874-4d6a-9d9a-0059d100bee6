# 用户模块序列化器

from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _


class UserSerializer(serializers.ModelSerializer):
    """
    用户序列化器
    用于用户信息的序列化和反序列化
    """
    # 添加自定义字段
    real_name = serializers.SerializerMethodField()
    phone = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ('id', 'username', 'email', 'real_name', 'phone', 'date_joined', 'last_login')
        read_only_fields = ('id', 'date_joined', 'last_login')

    def get_real_name(self, obj):
        # 从用户的first_name和last_name构建真实姓名
        if obj.first_name or obj.last_name:
            return f"{obj.last_name}{obj.first_name}"
        return ""

    def get_phone(self, obj):
        # 默认返回空字符串
        return ""


class UserRegisterSerializer(serializers.ModelSerializer):
    """
    用户注册序列化器
    用于用户注册
    """
    password = serializers.CharField(
        write_only=True,
        required=True,
        style={'input_type': 'password'}
    )
    password_confirm = serializers.CharField(
        write_only=True,
        required=True,
        style={'input_type': 'password'}
    )

    class Meta:
        model = User
        fields = ('username', 'email', 'password', 'password_confirm', 'real_name', 'phone')

    def validate(self, attrs):
        """
        验证密码是否一致
        """
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError({"password_confirm": _("两次密码不一致")})
        return attrs

    def create(self, validated_data):
        """
        创建用户
        """
        # 移除password_confirm字段
        validated_data.pop('password_confirm', None)
        # 创建用户
        user = User.objects.create_user(**validated_data)
        return user


class UserLoginSerializer(serializers.Serializer):
    """
    用户登录序列化器
    用于用户登录
    """
    username = serializers.CharField(required=True)
    password = serializers.CharField(
        write_only=True,
        required=True,
        style={'input_type': 'password'}
    )

    def validate(self, attrs):
        """
        验证用户名和密码
        """
        username = attrs.get('username')
        password = attrs.get('password')

        if username and password:
            # 打印调试信息
            print(f"\n尝试认证用户: {username}")

            # 直接检查用户密码
            try:
                user = User.objects.get(username=username)
                if user.check_password(password):
                    print(f"密码验证成功")
                    # 认证成功
                else:
                    print(f"密码验证失败")
                    msg = _('无法使用提供的凭据登录')
                    raise serializers.ValidationError(msg, code='authorization')
            except User.DoesNotExist:
                print(f"用户不存在")
                msg = _('无法使用提供的凭据登录')
                raise serializers.ValidationError(msg, code='authorization')
        else:
            msg = _('必须包含用户名和密码')
            raise serializers.ValidationError(msg, code='authorization')

        attrs['user'] = user
        return attrs


class PasswordChangeSerializer(serializers.Serializer):
    """
    密码修改序列化器
    用于用户修改密码
    """
    old_password = serializers.CharField(
        write_only=True,
        required=True,
        style={'input_type': 'password'}
    )
    new_password = serializers.CharField(
        write_only=True,
        required=True,
        style={'input_type': 'password'}
    )
    new_password_confirm = serializers.CharField(
        write_only=True,
        required=True,
        style={'input_type': 'password'}
    )

    def validate_old_password(self, value):
        """
        验证旧密码
        """
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError(_('旧密码不正确'))
        return value

    def validate(self, attrs):
        """
        验证新密码是否一致
        """
        if attrs['new_password'] != attrs['new_password_confirm']:
            raise serializers.ValidationError({"new_password_confirm": _("两次密码不一致")})
        return attrs

    def save(self, **kwargs):
        """
        保存新密码
        """
        user = self.context['request'].user
        user.set_password(self.validated_data['new_password'])
        user.save()
        return user
