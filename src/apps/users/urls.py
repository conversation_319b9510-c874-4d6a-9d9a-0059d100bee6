# 用户分析模块URL配置

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from rest_framework_simplejwt.views import TokenRefreshView
from . import views

# 创建路由器并注册用户视图集
router = DefaultRouter()
router.register(r'auth', views.UserViewSet)

urlpatterns = [
    # 用户认证相关路由
    path('', include(router.urls)),
    path('auth/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

    # 用户分析相关路由
    path('analytics/active/', views.UserAnalysisViewSet.as_view({'get': 'get_active_users'}), name='user-active'),
    path('analytics/<int:user_id>/behavior/', views.UserAnalysisViewSet.as_view({'get': 'get_user_behavior'}), name='user-behavior'),
    path('analytics/retention/', views.UserAnalysisViewSet.as_view({'get': 'get_user_retention'}), name='user-retention'),
]