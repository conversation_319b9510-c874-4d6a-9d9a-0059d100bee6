# 用户分析模块视图

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.views import APIView
from elasticsearch_dsl import Search, Q, A
from datetime import datetime, timedelta
import pandas as pd

from src.utils.es_client import es_client
from src.utils.date_utils import get_date_range
from src.config.constants import ElasticsearchIndices
from .serializers import UserLoginSerializer, PasswordChangeSerializer
from django.contrib.auth.models import User
from django.forms.models import model_to_dict




class UserAnalysisViewSet(viewsets.ViewSet):
    """
    用户分析视图集
    提供用户活跃度、行为习惯等分析接口
    """

    def get_active_users(self, request):
        """
        获取活跃用户统计
        支持日/周/月/季/年维度的活跃用户统计
        """
        time_dimension = request.query_params.get('dimension', 'daily')
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')

        # 获取日期范围
        start_date, end_date = get_date_range(time_dimension, start_date, end_date)

        # 构建ES查询
        s = Search(using=es_client, index=ElasticsearchIndices.OPERATION_LOG) \
            .filter('range', requestTime={'gte': start_date, 'lte': end_date})

        # 添加基本聚合 - 使用兼容7.10.0版本的语法
        active_users_agg = A('cardinality', field='operateUserId')
        s.aggs.metric('active_users', active_users_agg)

        # 按时间维度聚合
        if time_dimension == 'daily':
            date_agg = A('date_histogram', field='requestTime', calendar_interval='day')
            s.aggs.bucket('daily', date_agg)
            s.aggs['daily'].metric('active_users', active_users_agg)
        elif time_dimension == 'weekly':
            date_agg = A('date_histogram', field='requestTime', calendar_interval='week')
            s.aggs.bucket('weekly', date_agg)
            s.aggs['weekly'].metric('active_users', active_users_agg)
        elif time_dimension == 'monthly':
            date_agg = A('date_histogram', field='requestTime', calendar_interval='month')
            s.aggs.bucket('monthly', date_agg)
            s.aggs['monthly'].metric('active_users', active_users_agg)
        elif time_dimension == 'quarterly':
            date_agg = A('date_histogram', field='requestTime', calendar_interval='quarter')
            s.aggs.bucket('quarterly', date_agg)
            s.aggs['quarterly'].metric('active_users', active_users_agg)
        elif time_dimension == 'yearly':
            date_agg = A('date_histogram', field='requestTime', calendar_interval='year')
            s.aggs.bucket('yearly', date_agg)
            s.aggs['yearly'].metric('active_users', active_users_agg)

        # 执行查询
        response = s.execute()

        # 处理结果
        result = {
            'total_active_users': response.aggregations.active_users.value,
            'time_dimension': time_dimension,
            'data': []
        }

        # 根据时间维度获取结果
        time_agg = getattr(response.aggregations, time_dimension)
        for bucket in time_agg.buckets:
            result['data'].append({
                'time': bucket.key_as_string,
                'active_users': bucket.active_users.value
            })

        return Response(result)

    def get_user_behavior(self, request, user_id=None):
        """
        获取指定用户行为分析
        分析用户的操作习惯、常用功能等
        """
        if not user_id:
            return Response({"error": "用户ID不能为空"}, status=status.HTTP_400_BAD_REQUEST)

        # 获取时间范围
        days = int(request.query_params.get('days', 30))
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # 构建ES查询
        s = Search(using=es_client, index=ElasticsearchIndices.OPERATION_LOG) \
            .filter('term', operateUserId=user_id) \
            .filter('range', requestTime={'gte': start_date, 'lte': end_date})

        # 常用功能聚合 - 使用兼容7.10.0版本的语法
        top_features_agg = A('terms', field='requestUrl.keyword', size=10)
        s.aggs.bucket('top_features', top_features_agg)

        # 操作时间分布聚合 - 使用兼容7.10.0版本的语法
        time_dist_agg = A('date_histogram', field='requestTime', calendar_interval='hour')
        s.aggs.bucket('time_distribution', time_dist_agg)

        # 执行查询
        response = s.execute()

        # 处理结果
        result = {
            'user_id': user_id,
            'total_operations': response.hits.total.value,
            'date_range': {
                'start': start_date.isoformat(),
                'end': end_date.isoformat(),
                'days': days
            },
            'top_features': [],
            'time_distribution': []
        }

        # 处理常用功能
        for bucket in response.aggregations.top_features.buckets:
            result['top_features'].append({
                'url': bucket.key,
                'count': bucket.doc_count
            })

        # 处理时间分布
        for bucket in response.aggregations.time_distribution.buckets:
            result['time_distribution'].append({
                'hour': bucket.key_as_string,
                'count': bucket.doc_count
            })

        return Response(result)

    def get_user_retention(self, request):
        """
        获取用户留存率
        分析用户在不同时间段的留存情况
        """
        # 获取参数
        days = int(request.query_params.get('days', 7))  # 分析天数
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # 按天查询活跃用户
        daily_users = {}

        for i in range(days):
            current_date = start_date + timedelta(days=i)
            next_date = current_date + timedelta(days=1)

            # 构建ES查询
            s = Search(using=es_client, index=ElasticsearchIndices.OPERATION_LOG) \
                .filter('range', requestTime={'gte': current_date, 'lt': next_date})

            # 获取当天活跃用户ID - 使用兼容7.10.0版本的语法
            user_ids_agg = A('terms', field='operateUserId', size=10000)
            s.aggs.bucket('user_ids', user_ids_agg)

            # 执行查询
            response = s.execute()

            # 存储当天活跃用户ID
            user_ids = [bucket.key for bucket in response.aggregations.user_ids.buckets]
            daily_users[current_date.strftime('%Y-%m-%d')] = set(user_ids)

        # 计算留存率
        retention_data = []
        dates = list(daily_users.keys())

        for i, base_date in enumerate(dates[:-1]):  # 不包括最后一天
            base_users = daily_users[base_date]
            if not base_users:  # 避免除零错误
                continue

            retention_row = {
                'date': base_date,
                'new_users': len(base_users),
                'retention': []
            }

            # 计算后续天数的留存
            for j in range(i+1, len(dates)):
                current_date = dates[j]
                current_users = daily_users[current_date]
                retained_users = len(base_users.intersection(current_users))
                retention_rate = round(retained_users / len(base_users) * 100, 2)

                retention_row['retention'].append({
                    'date': current_date,
                    'rate': retention_rate,
                    'users': retained_users
                })

            retention_data.append(retention_row)

        result = {
            'date_range': {
                'start': start_date.strftime('%Y-%m-%d'),
                'end': end_date.strftime('%Y-%m-%d'),
            },
            'retention_data': retention_data
        }

        return Response(result)


class UserViewSet(viewsets.ModelViewSet):
    """
    用户视图集
    提供用户的CRUD操作
    """
    queryset = User.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    # 移除注册功能，使用默认管理员账号

    @action(detail=False, methods=['post'], permission_classes=[permissions.AllowAny])
    def login(self, request):
        """
        用户登录
        支持system_type参数，可选值为api(默认)、oss、boss
        """
        # 获取system_type参数，但不影响登录逻辑
        system_type = request.query_params.get('system_type', 'api')

        # 调试信息
        print(f"\n\n登录请求数据: {request.data}")
        print(f"Content-Type: {request.content_type}")

        # 检查用户是否存在
        from django.contrib.auth.models import User
        username = request.data.get('username')
        if username:
            user_exists = User.objects.filter(username=username).exists()
            print(f"用户 '{username}' 存在: {user_exists}")
            if user_exists:
                user = User.objects.get(username=username)
                print(f"用户ID: {user.id}, 是否激活: {user.is_active}")

        serializer = UserLoginSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            user = serializer.validated_data['user']
            refresh = RefreshToken.for_user(user)
            # 更新最后登录时间
            user.last_login = datetime.now()
            user.save(update_fields=['last_login'])

            # 构建用户信息
            user_data = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'real_name': f"{user.last_name}{user.first_name}" if (user.last_name or user.first_name) else "",
                'phone': "",  # 默认空字符串
                'date_joined': user.date_joined,
                'last_login': user.last_login
            }

            # 构建响应
            response_data = {
                'refresh': str(refresh),
                'access': str(refresh.access_token),
                'user': user_data,
                'system_type': system_type  # 将system_type包含在响应中
            }

            return Response(response_data)
        else:
            print(f"序列化器验证错误: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'], permission_classes=[permissions.IsAuthenticated])
    def me(self, request):
        """
        获取当前用户信息
        """
        user = request.user
        user_data = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'real_name': f"{user.last_name}{user.first_name}" if (user.last_name or user.first_name) else "",
            'phone': "",  # 默认空字符串
            'date_joined': user.date_joined,
            'last_login': user.last_login
        }
        return Response(user_data)

    @action(detail=False, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def change_password(self, request):
        """
        修改密码
        """
        serializer = PasswordChangeSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response({'detail': '密码修改成功'}, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def logout(self, request):
        """
        用户登出
        """
        # 在JWT中，登出只需要客户端删除本地存储的token
        # 这里只是提供一个接口，不需要实际的处理
        return Response({'detail': '登出成功'}, status=status.HTTP_200_OK)