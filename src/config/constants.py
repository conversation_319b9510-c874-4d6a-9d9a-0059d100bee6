# 系统常量定义

# 导入settings
from django.conf import settings

# 打印调试信息
import logging
logger = logging.getLogger(__name__)

# 检查settings是否包含ELASTICSEARCH_INDICES
try:
    es_indices = settings.ELASTICSEARCH_INDICES
    logger.info(f"constants.py: 成功读取ELASTICSEARCH_INDICES配置: {es_indices}")
except Exception as e:
    logger.error(f"constants.py: 无法读取ELASTICSEARCH_INDICES配置: {str(e)}")
    # 设置默认值
    es_indices = {'logs': 'tgtweb_apioperatelog'}

# Elasticsearch索引名称
class ElasticsearchIndices:
    """Elasticsearch索引名称常量
    从 settings.ELASTICSEARCH_INDICES 中获取索引名称
    为了兼容性，保留了原来的属性名称
    """
    # 索引名称常量
    API_INDEX = 'tgtweb_apioperatelog'
    OSS_INDEX = 'tgtweb_ossoperatelog'
    BOSS_INDEX = 'tgtweb_operatelog'

    # 字段映射
    FIELD_MAPPINGS = {
        'api': {
            'cost': 'cost',
            'ip': 'ipAddress',
            'operate_type': 'operateType',
            'user_id': 'accountId',
            'result': 'result',
            'create_time': 'createTime',
            'request_path': 'requestPath',
            'class_name': 'className',
            'method_name': 'methodName'
        },
        'oss': {
            'cost': 'cost',
            'ip': 'ipAddress',
            'operate_type': 'operateType',
            'user_id': 'operator',
            'result': 'result',
            'create_time': 'createTime',
            'request_path': 'requestPath',
            'class_name': 'className',
            'method_name': 'methodName'
        },
        'boss': {
            'cost': 'costTime',
            'ip': 'ip',
            'operate_type': 'operateType',
            'user_id': 'operateUserNameAnalyzed',  # 使用operateUserNameAnalyzed而不是operateUserNameAnalyzed.keyword
            'result': 'operateResult',
            'create_time': 'requestTime',
            'request_path': 'requestUrl',
            'class_name': 'requestNameAnalyzed',
            'method_name': 'requestNameAnalyzed',
            'method_param': 'requestParam'
        }
    }

    def get_index_by_system_type(self, system_type='api'):
        """根据系统类型获取索引名称"""
        if system_type == 'oss':
            return self.OSS_INDEX
        elif system_type == 'boss':
            return self.BOSS_INDEX
        else:  # 默认为api
            return self.API_INDEX

    def get_field_mapping(self, system_type='api'):
        """根据系统类型获取字段映射"""
        return self.FIELD_MAPPINGS.get(system_type, self.FIELD_MAPPINGS['api'])

    @property
    def OPERATION_LOG(self):
        """ 操作日志索引 """
        try:
            index = settings.ELASTICSEARCH_INDICES.get('logs', 'tgtweb_apioperatelog')
            logger.info(f"OPERATION_LOG: 使用索引 {index}")
            return index
        except Exception as e:
            logger.error(f"OPERATION_LOG: 无法获取索引配置: {str(e)}")
            return 'tgtweb_apioperatelog'

    @property
    def OPERATION_LOG_WILDCARD(self):
        """ 带通配符的操作日志索引 """
        try:
            index = settings.ELASTICSEARCH_INDICES.get('logs', 'tgtweb_apioperatelog')
            wildcard = f"{index}-*" if not index.endswith('-*') else index
            logger.info(f"OPERATION_LOG_WILDCARD: 使用通配符索引 {wildcard}")
            return wildcard
        except Exception as e:
            logger.error(f"OPERATION_LOG_WILDCARD: 无法获取索引配置: {str(e)}")
            return 'tgtweb_apioperatelog-*'

    def get_operation_log(self, system_type='api'):
        """根据系统类型获取操作日志索引"""
        return self.get_index_by_system_type(system_type)

    def get_operation_log_wildcard(self, system_type='api'):
        """根据系统类型获取带通配符的操作日志索引"""
        # 直接使用精确的索引名称，不使用通配符
        index = self.get_index_by_system_type(system_type)
        logger.info(f"使用索引: {index}")
        return index

# 创建单例
ElasticsearchIndices = ElasticsearchIndices()