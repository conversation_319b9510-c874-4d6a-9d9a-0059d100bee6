# Django项目设置

import os
import logging
from pathlib import Path

# 导入配置加载器
from src.config.config_loader import config

# 导入日志配置
from src.config.logging_config import configure_logging

# 构建路径
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# 安全设置 - 从配置文件读取
SECRET_KEY = config.get('app.secret_key', 'django-insecure-your-secret-key-here')
DEBUG = config.get('app.debug', True)
ALLOWED_HOSTS = config.get('app.allowed_hosts', ['*'])

# 应用定义
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    # 第三方应用
    'rest_framework',
    'rest_framework_simplejwt',
    'corsheaders',
    # 自定义应用
    'src.apps.users',
    'src.apps.features',
    'src.apps.analytics',
    'src.apps.reports',
    'src.apps.tasks',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    # 自定义中间件
    'src.middleware.performance_middleware.PerformanceMiddleware',  # 性能监控中间件（放在前面以准确记录耗时）
    'src.middleware.auth_middleware.AuthenticationMiddleware',  # 认证中间件
]

ROOT_URLCONF = 'src.config.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'src.config.wsgi.application'

# 数据库配置 - 从配置文件读取
DATABASES = {
    'default': config.get_database_config()
}

# 添加MySQL版本兼容性设置
import pymysql

# 使用PyMySQL替代MySQLdb来支持MySQL 5.7
pymysql.version_info = (1, 4, 6, 'final', 0)  # 伪装成MySQLdb
pymysql.install_as_MySQLdb()

# 为Django 2.2设置DateTimeField类型
from django.db.backends.mysql.base import DatabaseWrapper
DatabaseWrapper.data_types['DateTimeField'] = 'datetime'  # 使用datetime而不是datetime(6)

# Elasticsearch配置 - 从配置文件读取
ELASTICSEARCH_CONFIG = config.get_elasticsearch_config()

# ES索引配置
ELASTICSEARCH_INDICES = config.get('elasticsearch.indices', {
    'logs': 'tgtweb_apioperatelog',
})

# ES查询配置
ELASTICSEARCH_QUERY_CONFIG = config.get('elasticsearch.query', {
    'size_limit': 10000,
    'scroll_timeout': '1m',
    'aggs_size_limit': 1000,
})

# Redis缓存配置 - 从配置文件读取
CACHES = {
    "default": config.get_redis_config()
}

# 密码验证
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# 国际化 - 从配置文件读取
LANGUAGE_CODE = config.get('app.language_code', 'zh-hans')
TIME_ZONE = config.get('app.timezone', 'Asia/Shanghai')
USE_I18N = True
USE_TZ = False  # 禁用时区转换，直接使用本地时间（北京时间）

# 静态文件设置
STATIC_URL = 'static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'static')

# 默认主键字段类型 - 调整为Django 2.2兼容的设置
# 明确指定使用AutoField，与现有模型保持一致
DEFAULT_AUTO_FIELD = 'django.db.models.AutoField'

# REST Framework设置 - 从配置文件读取
REST_FRAMEWORK = {
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': config.get('rest_framework.page_size', 10),
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.BasicAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': config.get('rest_framework.default_permission_classes', [
        'rest_framework.permissions.IsAuthenticated',
    ]),
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ],
}

# CORS设置 - 从配置文件读取
CORS_ALLOW_ALL_ORIGINS = config.get('cors.allow_all_origins', True)

# 设置默认的自动字段类型，避免Django 3.2+的警告
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# 自定义用户模型设置
# 暂时使用默认的Django User模型
# AUTH_USER_MODEL = 'users.User'

# JWT设置 - 从配置文件读取
SIMPLE_JWT = config.get_jwt_config(SECRET_KEY)

# Django日志配置 - 使用RotatingFileHandler避免多进程冲突
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{asctime} - {name} - {levelname} - {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'django_file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'django-web.log'),
            'maxBytes': 50*1024*1024,  # 50MB
            'backupCount': 10,
            'encoding': 'utf-8',
            'delay': True,
            'formatter': 'verbose',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'django_error_file': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'django-error.log'),
            'maxBytes': 10*1024*1024,  # 10MB
            'backupCount': 10,
            'encoding': 'utf-8',
            'delay': True,
            'formatter': 'verbose',
        },

    },
    'loggers': {
        'django': {
            'handlers': ['django_file', 'console', 'django_error_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.server': {
            'handlers': ['django_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.request': {
            'handlers': ['django_file', 'console', 'django_error_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'src': {
            'handlers': ['django_file', 'console', 'django_error_file'],
            'level': 'INFO',
            'propagate': False,
        },
        'root': {
            'handlers': ['django_file', 'console', 'django_error_file'],
            'level': 'INFO',
        },
    },
}

# 配置日志系统
if os.environ.get('DISABLE_DJANGO_LOGGING') != 'True':
    configure_logging()