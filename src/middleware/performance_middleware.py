#!/usr/bin/env python
# 性能监控中间件
# 记录所有API接口的请求耗时

import time
import logging
import json
from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse
from django.urls import resolve
from django.conf import settings

# 使用Django的web日志记录器
web_logger = logging.getLogger('django.server')

class PerformanceMiddleware(MiddlewareMixin):
    """
    性能监控中间件
    自动记录所有API请求的耗时信息
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
        
    def process_request(self, request):
        """
        请求开始时记录开始时间
        """
        # 记录请求开始时间（使用高精度时间）
        request._start_time = time.perf_counter()
        request._start_timestamp = time.time()
        
        # 调试输出（已禁用）
        # print(f"[DEBUG] Performance middleware: Processing request {request.method} {request.path}")
        
        # 记录请求的基本信息
        request._request_info = {
            'method': request.method,
            'path': request.path,
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'remote_addr': self.get_client_ip(request),
            'content_type': request.META.get('CONTENT_TYPE', ''),
            'content_length': request.META.get('CONTENT_LENGTH', 0),
        }
        
        return None
        
    def process_response(self, request, response):
        """
        请求结束时计算耗时并记录日志
        """
        # 检查是否有开始时间（防止某些情况下没有经过process_request）
        if not hasattr(request, '_start_time'):
            return response
            
        # 计算请求耗时（毫秒）
        end_time = time.perf_counter()
        duration_ms = round((end_time - request._start_time) * 1000, 2)
        
        # 获取请求信息
        request_info = getattr(request, '_request_info', {})
        
        # 获取响应状态码
        status_code = getattr(response, 'status_code', 0)
        

            
        # 获取用户信息
        username = 'anonymous'
        if hasattr(request, 'user') and request.user.is_authenticated:
            username = getattr(request.user, 'username', 'anonymous')
                
        # 格式化简化的日志消息，只添加耗时信息
        log_message = (
            f"{request_info.get('method', '')} {request_info.get('path', '')} "
            f"[{status_code}] - {duration_ms}ms - {username}"
        )

        # 记录到Django web日志，只记录耗时信息
        web_logger.info(log_message)
            
        # 如果是API请求且启用了调试模式，在响应头中添加耗时信息
        if self.is_api_request(request) and settings.DEBUG:
            response['X-Response-Time'] = f"{duration_ms}ms"
            response['X-Request-ID'] = f"{int(request._start_timestamp * 1000)}"
            
        return response
        
    def process_exception(self, request, exception):
        """
        处理异常时也记录耗时
        """
        if not hasattr(request, '_start_time'):
            return None
            
        # 计算请求耗时
        end_time = time.perf_counter()
        duration_ms = round((end_time - request._start_time) * 1000, 2)
        
        # 获取请求信息
        request_info = getattr(request, '_request_info', {})
        
        # 记录异常日志
        log_message = (
            f"{request_info.get('method', '')} {request_info.get('path', '')} "
            f"[EXCEPTION] - {duration_ms}ms - Exception: {str(exception)}"
        )

        web_logger.error(log_message)
        
        return None
        
    def get_client_ip(self, request):
        """
        获取客户端真实IP地址
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '')
        return ip
        
    def is_api_request(self, request):
        """
        判断是否为API请求
        """
        return (
            request.path.startswith('/api/') or
            request.content_type == 'application/json' or
            'application/json' in request.META.get('HTTP_ACCEPT', '')
        )
        

