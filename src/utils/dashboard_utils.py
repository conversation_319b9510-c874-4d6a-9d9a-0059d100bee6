# 首页仪表盘工具函数

from datetime import datetime, timedelta
import math
from elasticsearch_dsl import Search, Q, A
from src.utils.es_client import es_client
from src.config.constants import ElasticsearchIndices


def get_active_users(time_range=None, system_type='api'):
    """
    获取指定时间范围内的活跃用户数
    :param time_range: 时间范围（小时），默认为当天0点到当前时间
    :param system_type: 系统类型，可选值为api(默认)、oss、boss
    :return: 活跃用户数及环比变化率
    """
    # 使用当前时间，并考虑到北京时间（UTC+8）
    now = datetime.now()
    # 如果没有指定时间范围，则默认为当天0点到当前时间
    if time_range is None:
        # 北京时间当天零点
        start_time = datetime(now.year, now.month, now.day, 0, 0, 0)
    else:
        start_time = now - timedelta(hours=time_range)
    # 计算上一个相同时间段
    time_diff = now - start_time
    previous_start_time = start_time - time_diff

    # 格式化日期为Elasticsearch可接受的格式 (使用Elasticsearch支持的格式)
    now_str = now.strftime('%Y-%m-%d %H:%M:%S')
    start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
    previous_start_time_str = previous_start_time.strftime('%Y-%m-%d %H:%M:%S')

    # 获取ES索引和字段映射
    log_index = ElasticsearchIndices.get_operation_log_wildcard(system_type)
    field_mapping = ElasticsearchIndices.get_field_mapping(system_type)

    # 当前时间段查询
    # 使用lte而不是lt，以确保包含结束时间
    current_query = Search(using=es_client, index=log_index) \
        .filter('range', **{field_mapping['create_time']: {'gte': start_time_str, 'lte': now_str}})

    # 聚合查询不同用户数
    # 当system_type为boss时，使用operateUserId字段进行聚合
    if system_type == 'boss':
        current_query.aggs.bucket('unique_users', 'cardinality', field='operateUserId')
    else:
        current_query.aggs.bucket('unique_users', 'cardinality', field=field_mapping['user_id'])
    current_result = current_query.execute()
    current_count = current_result.aggregations.unique_users.value if hasattr(current_result, 'aggregations') else 0

    # 上一时间段查询（环比数据）
    # 使用lte而不是lt，以确保包含结束时间
    previous_query = Search(using=es_client, index=log_index) \
        .filter('range', **{field_mapping['create_time']: {'gte': previous_start_time_str, 'lte': start_time_str}})

    # 当system_type为boss时，使用operateUserId字段进行聚合
    if system_type == 'boss':
        previous_query.aggs.bucket('unique_users', 'cardinality', field='operateUserId')
    else:
        previous_query.aggs.bucket('unique_users', 'cardinality', field=field_mapping['user_id'])
    previous_result = previous_query.execute()
    previous_count = previous_result.aggregations.unique_users.value if hasattr(previous_result, 'aggregations') else 0

    # 计算环比变化率
    change_rate = ((current_count - previous_count) / previous_count * 100) if previous_count > 0 else 0

    return {
        'current': int(current_count),
        'change_rate': round(change_rate, 2)
    }


def get_function_calls(time_range=None, system_type='api'):
    """
    获取指定时间范围内的功能调用次数
    :param time_range: 时间范围（小时），默认为当天0点到当前时间
    :param system_type: 系统类型，可选值为api(默认)、oss、boss
    :return: 功能调用次数及环比变化率
    """
    # 使用当前时间，并考虑到北京时间（UTC+8）
    now = datetime.now()
    # 如果没有指定时间范围，则默认为当天0点到当前时间
    if time_range is None:
        # 北京时间当天零点
        start_time = datetime(now.year, now.month, now.day, 0, 0, 0)
    else:
        start_time = now - timedelta(hours=time_range)
    # 计算上一个相同时间段
    time_diff = now - start_time
    previous_start_time = start_time - time_diff

    # 格式化日期为Elasticsearch可接受的格式 (使用Elasticsearch支持的格式)
    now_str = now.strftime('%Y-%m-%d %H:%M:%S')
    start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
    previous_start_time_str = previous_start_time.strftime('%Y-%m-%d %H:%M:%S')

    # 获取ES索引和字段映射
    log_index = ElasticsearchIndices.get_operation_log_wildcard(system_type)
    field_mapping = ElasticsearchIndices.get_field_mapping(system_type)

    # 当前时间段查询
    # 使用lte而不是lt，以确保包含结束时间
    current_query = Search(using=es_client, index=log_index) \
        .filter('range', **{field_mapping['create_time']: {'gte': start_time_str, 'lte': now_str}})

    current_count = current_query.count()

    # 上一时间段查询（环比数据）
    # 使用lte而不是lt，以确保包含结束时间
    previous_query = Search(using=es_client, index=log_index) \
        .filter('range', **{field_mapping['create_time']: {'gte': previous_start_time_str, 'lte': start_time_str}})

    previous_count = previous_query.count()

    # 计算环比变化率
    change_rate = ((current_count - previous_count) / previous_count * 100) if previous_count > 0 else 0

    return {
        'current': current_count,
        'change_rate': round(change_rate, 2)
    }


def get_avg_response_time(time_range=None, system_type='api'):
    """
    获取指定时间范围内的平均响应时间
    :param time_range: 时间范围（小时），默认为当天0点到当前时间
    :param system_type: 系统类型，可选值为api(默认)、oss、boss
    :return: 平均响应时间及环比变化率
    """
    # 使用当前时间，并考虑到北京时间（UTC+8）
    now = datetime.now()
    # 如果没有指定时间范围，则默认为当天0点到当前时间
    if time_range is None:
        # 北京时间当天零点
        start_time = datetime(now.year, now.month, now.day, 0, 0, 0)
    else:
        start_time = now - timedelta(hours=time_range)
    # 计算上一个相同时间段
    time_diff = now - start_time
    previous_start_time = start_time - time_diff

    # 格式化日期为Elasticsearch可接受的格式 (使用Elasticsearch支持的格式)
    now_str = now.strftime('%Y-%m-%d %H:%M:%S')
    start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
    previous_start_time_str = previous_start_time.strftime('%Y-%m-%d %H:%M:%S')

    # 获取ES索引和字段映射
    log_index = ElasticsearchIndices.get_operation_log_wildcard(system_type)
    field_mapping = ElasticsearchIndices.get_field_mapping(system_type)

    # 当前时间段查询
    # 使用lte而不是lt，以确保包含结束时间
    current_query = Search(using=es_client, index=log_index) \
        .filter('range', **{field_mapping['create_time']: {'gte': start_time_str, 'lte': now_str}})

    current_query.aggs.metric('avg_time', 'avg', field=field_mapping['cost'])
    current_result = current_query.execute()
    current_avg = current_result.aggregations.avg_time.value if hasattr(current_result, 'aggregations') and current_result.aggregations.avg_time.value is not None else 0

    # 上一时间段查询（环比数据）
    # 使用lte而不是lt，以确保包含结束时间
    previous_query = Search(using=es_client, index=log_index) \
        .filter('range', **{field_mapping['create_time']: {'gte': previous_start_time_str, 'lte': start_time_str}})

    previous_query.aggs.metric('avg_time', 'avg', field=field_mapping['cost'])
    previous_result = previous_query.execute()
    previous_avg = previous_result.aggregations.avg_time.value if hasattr(previous_result, 'aggregations') and previous_result.aggregations.avg_time.value is not None else 0

    # 计算环比变化率
    change_rate = ((current_avg - previous_avg) / previous_avg * 100) if previous_avg > 0 else 0

    return {
        'current': round(current_avg, 2) if current_avg else 0,
        'change_rate': round(change_rate, 2)
    }


def get_error_rate(time_range=None, system_type='api'):
    """
    获取指定时间范围内的错误率
    :param time_range: 时间范围（小时），默认为当天0点到当前时间
    :param system_type: 系统类型，可选值为api(默认)、oss、boss
    :return: 错误率及环比变化率
    """
    # 使用当前时间，并考虑到北京时间（UTC+8）
    now = datetime.now()
    # 如果没有指定时间范围，则默认为当天0点到当前时间
    if time_range is None:
        # 北京时间当天零点
        start_time = datetime(now.year, now.month, now.day, 0, 0, 0)
    else:
        start_time = now - timedelta(hours=time_range)
    # 计算上一个相同时间段
    time_diff = now - start_time
    previous_start_time = start_time - time_diff

    # 格式化日期为Elasticsearch可接受的格式 (使用Elasticsearch支持的格式)
    now_str = now.strftime('%Y-%m-%d %H:%M:%S')
    start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
    previous_start_time_str = previous_start_time.strftime('%Y-%m-%d %H:%M:%S')

    # 获取ES索引和字段映射
    log_index = ElasticsearchIndices.get_operation_log_wildcard(system_type)
    field_mapping = ElasticsearchIndices.get_field_mapping(system_type)

    # 当前时间段查询
    # 使用lte而不是lt，以确保包含结束时间
    current_query = Search(using=es_client, index=log_index) \
        .filter('range', **{field_mapping['create_time']: {'gte': start_time_str, 'lte': now_str}})

    # 获取总请求数
    current_total = current_query.count()

    # 获取错误请求数
    current_error_query = current_query.filter(Q('term', **{field_mapping['result']: 1}))
    current_error_count = current_error_query.count()

    # 计算当前错误率
    current_error_rate = (current_error_count / current_total * 100) if current_total > 0 else 0

    # 上一时间段查询（环比数据）
    # 使用lte而不是lt，以确保包含结束时间
    previous_query = Search(using=es_client, index=log_index) \
        .filter('range', **{field_mapping['create_time']: {'gte': previous_start_time_str, 'lte': start_time_str}})

    # 获取总请求数
    previous_total = previous_query.count()

    # 获取错误请求数
    previous_error_query = previous_query.filter(Q('term', **{field_mapping['result']: 1}))
    previous_error_count = previous_error_query.count()

    # 计算上一时段错误率
    previous_error_rate = (previous_error_count / previous_total * 100) if previous_total > 0 else 0

    # 计算环比变化率
    change_rate = ((current_error_rate - previous_error_rate) / previous_error_rate * 100) if previous_error_rate > 0 else 0

    return {
        'current': round(current_error_rate, 2),
        'change_rate': round(change_rate, 2)
    }


from src.utils.es_client import with_new_client, create_new_client
import logging

# 获取日志记录器
logger = logging.getLogger(__name__)

def get_tps_and_response_time(time_period='5m', system_type='api'):
    """
    获取指定时间段内的TPS和平均响应时间

    :param time_period: 时间段，可选值：'5m'(5分钟), '1h'(1小时), '1d'(1天), '1w'(1周)
    :param system_type: 系统类型，可选值为api(默认)、oss、boss
    :return: 包含TPS和平均响应时间的时间序列数据
    """
    # 创建新的ES客户端
    client = create_new_client()
    try:
        now = datetime.now()

        # 根据时间段设置开始时间和时间间隔
        if time_period == '5m':
            start_time = now - timedelta(minutes=5)
            interval = '1s'  # 每秒一个数据点
            date_format = 'HH:mm:ss'
        elif time_period == '1h':
            start_time = now - timedelta(hours=1)
            interval = '1m'  # 每分钟一个数据点
            date_format = 'HH:mm'
        elif time_period == '1d':
            start_time = now - timedelta(days=1)
            interval = '5m'  # 每5分钟一个数据点
            date_format = 'MM-dd HH:mm'
        elif time_period == '1w':
            start_time = now - timedelta(weeks=1)
            interval = '10m'  # 每10分钟一个数据点
            date_format = 'MM-dd HH:mm'
        else:
            # 默认5分钟
            start_time = now - timedelta(minutes=5)
            interval = '1s'
            date_format = 'HH:mm:ss'

        # 格式化日期为Elasticsearch可接受的格式
        now_str = now.strftime('%Y-%m-%d %H:%M:%S')
        start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')

        # 获取ES索引和字段映射
        log_index = ElasticsearchIndices.get_operation_log_wildcard(system_type)
        field_mapping = ElasticsearchIndices.get_field_mapping(system_type)

        # 构建ES查询
        # 使用lte而不是lt，以确保包含结束时间
        s = Search(using=client, index=log_index) \
            .filter('range', **{field_mapping['create_time']: {'gte': start_time_str, 'lte': now_str}})

        # 添加日期直方图聚合，按指定间隔分组
        # 注意：Elasticsearch 7.10.0使用interval而非calendar_interval
        # 不要使用format参数，因为Elasticsearch返回的格式不正确
        s.aggs.bucket('time_buckets', 'date_histogram',
                      field=field_mapping['create_time'],
                      interval=interval) \
.metric('avg_response_time', 'avg', field=field_mapping['cost'])

        # 执行查询
        response = s.execute()

        # 生成完整的时间序列
        time_series = []

        # 生成所有时间点
        if time_period == '5m':
            # 5分钟，每秒1个点，共300个点
            total_points = 300
            time_format = '%H:%M:%S'
        elif time_period == '1h':
            # 1小时，每分钟1个点，共60个点
            total_points = 60
            time_format = '%H:%M'
        elif time_period == '1d':
            # 1天，每5分钟1个点，共288个点
            total_points = 288
            time_format = '%m-%d %H:%M'
        elif time_period == '1w':
            # 1周，每10分钟1个点，共1008个点
            total_points = 1008
            time_format = '%m-%d %H:%M'
        else:
            # 默认5分钟
            total_points = 300
            time_format = '%H:%M:%S'

        # 创建所有时间点的字典，初始化为0
        time_points = {}

        if time_period == '5m':
            # 每秒1个点
            for i in range(total_points):
                point_time = start_time + timedelta(seconds=i)
                time_key = point_time.strftime(time_format)
                time_points[time_key] = {
                    'timestamp': time_key,
                    'tps': 0,
                    'avg_response_time': 0
                }
        elif time_period == '1h':
            # 每分钟1个点
            for i in range(total_points):
                point_time = start_time + timedelta(minutes=i)
                time_key = point_time.strftime(time_format)
                time_points[time_key] = {
                    'timestamp': time_key,
                    'tps': 0,
                    'avg_response_time': 0
                }
        elif time_period == '1d':
            # 每5分钟1个点，并且是固定的时间点（能被5整除）
            # 先将开始时间调整为能被5整除的分钟数
            start_minutes = (start_time.hour * 60 + start_time.minute) // 5 * 5
            adjusted_start = datetime(start_time.year, start_time.month, start_time.day,
                                      start_minutes // 60, start_minutes % 60, 0)

            for i in range(total_points):
                point_time = adjusted_start + timedelta(minutes=i*5)
                time_key = point_time.strftime(time_format)
                time_points[time_key] = {
                    'timestamp': time_key,
                    'tps': 0,
                    'avg_response_time': 0
                }
        elif time_period == '1w':
            # 每10分钟1个点，并且是固定的时间点（能被10整除）
            # 先将开始时间调整为能被10整除的分钟数
            start_minutes = (start_time.hour * 60 + start_time.minute) // 10 * 10
            adjusted_start = datetime(start_time.year, start_time.month, start_time.day,
                                      start_minutes // 60, start_minutes % 60, 0)

            for i in range(total_points):
                point_time = adjusted_start + timedelta(minutes=i*10)
                time_key = point_time.strftime(time_format)
                time_points[time_key] = {
                    'timestamp': time_key,
                    'tps': 0,
                    'avg_response_time': 0
                }

        # 如果有聚合数据，填充实际值
        if hasattr(response, 'aggregations') and hasattr(response.aggregations, 'time_buckets'):
            for bucket in response.aggregations.time_buckets.buckets:
                # 获取时间点 - 使用key_as_string而不是将key转换为时间
                # 因为时区问题，直接使用key_as_string更可靠
                if hasattr(bucket, 'key_as_string'):
                    # 从字符串中提取小时和分钟
                    try:
                        dt = datetime.strptime(bucket.key_as_string, '%Y-%m-%d %H:%M:%S')
                        time_key = dt.strftime(time_format)
                    except Exception:
                        # 如果解析失败，使用备用方法
                        bucket_time = datetime.fromtimestamp(bucket.key / 1000)  # 毫秒转秒
                        time_key = bucket_time.strftime(time_format)
                else:
                    # 如果没有key_as_string，使用key
                    bucket_time = datetime.fromtimestamp(bucket.key / 1000)  # 毫秒转秒
                    time_key = bucket_time.strftime(time_format)

                # 计算TPS
                # 对于不同的时间间隔，需要进行不同的计算
                # 确保如果有数据，最小值是0.01
                if bucket.doc_count > 0:
                    if interval == '1s':
                        # 每秒的请求数就是TPS
                        tps = max(0.01, bucket.doc_count)
                    elif interval == '1m':
                        # 每分钟的请求数需要除以60得到每秒的请求数
                        tps = max(0.01, bucket.doc_count / 60)
                    elif interval == '5m':
                        # 每5分钟的请求数需要除以300得到每秒的请求数
                        tps = max(0.01, bucket.doc_count / 300)
                    elif interval == '10m':
                        # 每10分钟的请求数需要除以600得到每秒的请求数
                        tps = max(0.01, bucket.doc_count / 600)
                    else:
                        tps = max(0.01, bucket.doc_count)
                else:
                    tps = 0

                # 获取平均响应时间
                avg_response_time = bucket.avg_response_time.value if hasattr(bucket, 'avg_response_time') and bucket.avg_response_time.value is not None else 0

                # 替换字典中的值
                if time_key in time_points:
                    time_points[time_key] = {
                        'timestamp': time_key,
                        'tps': round(tps, 2),  # 保留两位小数
                        'avg_response_time': round(avg_response_time, 2)  # 保留两位小数
                    }

        # 将字典转换为列表，并按时间戳排序
        time_series = []

        if time_period == '5m':
            # 每秒1个点
            for i in range(total_points):
                point_time = start_time + timedelta(seconds=i)
                time_key = point_time.strftime(time_format)
                if time_key in time_points:
                    time_series.append(time_points[time_key])
        elif time_period == '1h':
            # 每分钟1个点
            for i in range(total_points):
                point_time = start_time + timedelta(minutes=i)
                time_key = point_time.strftime(time_format)
                if time_key in time_points:
                    time_series.append(time_points[time_key])
        elif time_period == '1d':
            # 每5分钟1个点，并且是固定的时间点（能被5整除）
            start_minutes = (start_time.hour * 60 + start_time.minute) // 5 * 5
            adjusted_start = datetime(start_time.year, start_time.month, start_time.day,
                                      start_minutes // 60, start_minutes % 60, 0)

            for i in range(total_points):
                point_time = adjusted_start + timedelta(minutes=i*5)
                time_key = point_time.strftime(time_format)
                if time_key in time_points:
                    time_series.append(time_points[time_key])
        elif time_period == '1w':
            # 每10分钟1个点，并且是固定的时间点（能被10整除）
            start_minutes = (start_time.hour * 60 + start_time.minute) // 10 * 10
            adjusted_start = datetime(start_time.year, start_time.month, start_time.day,
                                      start_minutes // 60, start_minutes % 60, 0)

            for i in range(total_points):
                point_time = adjusted_start + timedelta(minutes=i*10)
                time_key = point_time.strftime(time_format)
                if time_key in time_points:
                    time_series.append(time_points[time_key])

        # 计算整体平均值
        avg_tps = sum(item['tps'] for item in time_series) / len(time_series) if time_series else 0
        avg_response_time = sum(item['avg_response_time'] for item in time_series) / len(time_series) if time_series else 0

        result = {
            'time_series': time_series,
            'summary': {
                'avg_tps': round(avg_tps, 2),
                'avg_response_time': round(avg_response_time, 2),
                'time_period': time_period,
                'total_requests': sum(bucket.doc_count for bucket in response.aggregations.time_buckets.buckets) if hasattr(response, 'aggregations') else 0,
                'system_type': system_type
            }
        }

    finally:
        # 无论函数是否成功执行，都关闭客户端
        try:
            client.close()
            logger.debug("关闭Elasticsearch连接")
        except Exception as e:
            logger.warning(f"关闭Elasticsearch连接时出错: {str(e)}")

    return result


def get_interface_tps_and_records(time_period='5m', page=1, page_size=10, filter_url=None, include_time_series=True, system_type='api'):
    """
    获取按接口分组的TPS和响应时间数据，以及每个接口的最新5条调用记录

    :param time_period: 时间段，可选值：'5m'(5分钟), '1h'(1小时), '1d'(1天), '1w'(1周)
    :param page: 当前页码，从1开始
    :param page_size: 每页显示的接口数量
    :param filter_url: 过滤特定的接口URL，如果提供则只返回该URL的数据
    :param include_time_series: 是否包含完整的时间序列数据
    :param system_type: 系统类型，可选值为api(默认)、oss、boss
    :return: 包含接口TPS、响应时间和调用记录的数据
    """
    now = datetime.now()

    # 根据时间段设置开始时间和时间间隔
    if time_period == '5m':
        start_time = now - timedelta(minutes=5)
        interval = '1s'  # 每秒一个数据点
    elif time_period == '1h':
        start_time = now - timedelta(hours=1)
        interval = '1m'  # 每分钟一个数据点
    elif time_period == '1d':
        start_time = now - timedelta(days=1)
        interval = '5m'  # 每5分钟一个数据点
    elif time_period == '1w':
        start_time = now - timedelta(weeks=1)
        interval = '10m'  # 每10分钟一个数据点
    else:
        # 默认5分钟
        start_time = now - timedelta(minutes=5)
        interval = '1s'

    # 格式化日期为Elasticsearch可接受的格式
    now_str = now.strftime('%Y-%m-%d %H:%M:%S')
    start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')

    # 获取ES索引和字段映射
    log_index = ElasticsearchIndices.get_operation_log_wildcard(system_type)
    field_mapping = ElasticsearchIndices.get_field_mapping(system_type)

    # 构建ES查询 - 按接口分组
    # 使用lte而不是lt，以确保包含结束时间
    s = Search(using=es_client, index=log_index) \
        .filter('range', **{field_mapping['create_time']: {'gte': start_time_str, 'lte': now_str}})

    # 如果提供了过滤URL，添加过滤条件
    if filter_url:
        s = s.filter('term', **{field_mapping['request_path']: filter_url})

    # 添加接口聚合
    # 注意：requestPath字段本身就是keyword类型，不需要.keyword后缀
    s.aggs.bucket('interfaces', 'terms', field=field_mapping['request_path'], size=1000) \
          .metric('avg_response_time', 'avg', field=field_mapping['cost']) \
          .metric('max_response_time', 'max', field=field_mapping['cost']) \
          .metric('min_response_time', 'min', field=field_mapping['cost'])

    # 执行查询
    response = s.execute()

    # 处理接口聚合结果
    interfaces = []
    total_interfaces = 0

    if hasattr(response, 'aggregations') and hasattr(response.aggregations, 'interfaces'):
        # 如果提供了过滤URL，则只处理该URL的数据
        if filter_url:
            # 在聚合桶中查找匹配的URL
            matching_buckets = [bucket for bucket in response.aggregations.interfaces.buckets if bucket.key == filter_url]
            total_interfaces = len(matching_buckets)
            page_buckets = matching_buckets  # 当过滤特定接口时，不进行分页
        else:
            # 获取总接口数
            total_interfaces = len(response.aggregations.interfaces.buckets)

            # 计算分页
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size

            # 获取当前页的接口桶
            page_buckets = response.aggregations.interfaces.buckets[start_idx:end_idx]

        # 处理每个接口的数据
        for bucket in page_buckets:
            interface_url = bucket.key
            request_count = bucket.doc_count

            # 计算TPS
            # 对于不同的时间间隔，需要进行不同的计算
            time_diff_seconds = (now - start_time).total_seconds()
            tps = request_count / time_diff_seconds if time_diff_seconds > 0 else 0

            # 获取响应时间统计
            avg_response_time = bucket.avg_response_time.value if hasattr(bucket, 'avg_response_time') and bucket.avg_response_time.value is not None else 0
            max_response_time = bucket.max_response_time.value if hasattr(bucket, 'max_response_time') and bucket.max_response_time.value is not None else 0
            min_response_time = bucket.min_response_time.value if hasattr(bucket, 'min_response_time') and bucket.min_response_time.value is not None else 0

            # 获取该接口的最新5条调用记录
            recent_records = get_recent_interface_records(interface_url, 5, start_time_str, now_str, system_type)

            # 准备接口数据
            interface_data = {
                'interface_url': interface_url,
                'request_count': request_count,
                'tps': round(tps, 2),
                'avg_response_time': round(avg_response_time, 2),
                'max_response_time': round(max_response_time, 2),
                'min_response_time': round(min_response_time, 2),
                'recent_records': recent_records
            }

            # 如果需要包含时间序列数据
            if include_time_series:
                # 获取该接口的时间序列数据
                interface_data['time_series'] = get_interface_time_series(interface_url, time_period, start_time_str, now_str, system_type)

            # 添加到接口列表
            interfaces.append(interface_data)

    # 按TPS降序排序
    interfaces.sort(key=lambda x: x['tps'], reverse=True)

    # 计算分页信息
    total_pages = math.ceil(total_interfaces / page_size) if total_interfaces > 0 else 0

    return {
        'interfaces': interfaces,
        'pagination': {
            'page': page,
            'page_size': page_size,
            'total_interfaces': total_interfaces,
            'total_pages': total_pages
        },
        'summary': {
            'time_period': time_period,
            'start_time': start_time_str,
            'end_time': now_str,
            'system_type': system_type
        }
    }


def get_recent_interface_records(interface_url, limit, start_time_str, end_time_str, system_type='api'):
    """
    获取指定接口的最近调用记录

    :param interface_url: 接口URL
    :param limit: 返回记录数量
    :param start_time_str: 开始时间字符串
    :param end_time_str: 结束时间字符串
    :param system_type: 系统类型，可选值为api(默认)、oss、boss
    :return: 最近的调用记录列表
    """
    # 获取ES索引和字段映射
    log_index = ElasticsearchIndices.get_operation_log_wildcard(system_type)
    field_mapping = ElasticsearchIndices.get_field_mapping(system_type)

    # 构建ES查询
    # 使用lte而不是lt，以确保包含结束时间
    s = Search(using=es_client, index=log_index) \
        .filter('term', **{field_mapping['request_path']: interface_url}) \
        .filter('range', **{field_mapping['create_time']: {'gte': start_time_str, 'lte': end_time_str}}) \
        .sort(f"-{field_mapping['create_time']}")  # 按请求时间降序排序，获取最新的记录

    # 限制返回数量
    s = s[:limit]

    # 执行查询
    response = s.execute()

    # 处理结果
    records = []

    for hit in response:
        # 提取需要的字段
        record = {
            'request_time': getattr(hit, field_mapping['create_time'], ''),
            'cost_time': getattr(hit, field_mapping['cost'], 0),
            'operate_result': getattr(hit, field_mapping['result'], ''),
            'user_id': getattr(hit, field_mapping['user_id'], ''),
            'user_name': getattr(hit, field_mapping['class_name'], ''),  # 使用class_name字段
            'ip': getattr(hit, field_mapping['ip'], '')
        }
        records.append(record)

    return records


def get_interface_time_series(interface_url, time_period, start_time_str, end_time_str, system_type='api'):
    """
    获取指定接口的时间序列数据

    :param interface_url: 接口URL
    :param time_period: 时间段，可选值：'5m'(5分钟), '1h'(1小时), '1d'(1天), '1w'(1周)
    :param start_time_str: 开始时间字符串
    :param end_time_str: 结束时间字符串
    :param system_type: 系统类型，可选值为api(默认)、oss、boss
    :return: 包含TPS和响应时间的时间序列数据
    """
    # 解析开始和结束时间
    start_time = datetime.strptime(start_time_str, '%Y-%m-%d %H:%M:%S')
    end_time = datetime.strptime(end_time_str, '%Y-%m-%d %H:%M:%S')

    # 根据时间段设置时间间隔
    if time_period == '5m':
        # 5分钟，每秒1个点，共300个点
        interval = '1s'
        total_points = 300
        time_format = '%H:%M:%S'
    elif time_period == '1h':
        # 1小时，每分钟1个点，共60个点
        interval = '1m'
        total_points = 60
        time_format = '%H:%M'
    elif time_period == '1d':
        # 1天，每5分钟1个点，共288个点
        interval = '5m'
        total_points = 288
        time_format = '%m-%d %H:%M'
    elif time_period == '1w':
        # 1周，每10分钟1个点，共1008个点
        interval = '10m'
        total_points = 1008
        time_format = '%m-%d %H:%M'
    else:
        # 默认5分钟
        interval = '1s'
        total_points = 300
        time_format = '%H:%M:%S'

    # 构建ES查询
    # 获取ES索引和字段映射
    log_index = ElasticsearchIndices.get_operation_log_wildcard(system_type)
    field_mapping = ElasticsearchIndices.get_field_mapping(system_type)

    # 使用lte而不是lt，以确保包含结束时间
    s = Search(using=es_client, index=log_index) \
        .filter('term', **{field_mapping['request_path']: interface_url}) \
        .filter('range', **{field_mapping['create_time']: {'gte': start_time_str, 'lte': end_time_str}})

    # 添加日期直方图聚合，按指定间隔分组
    # 注意：不要使用format参数，因为Elasticsearch返回的格式不正确
    s.aggs.bucket('time_buckets', 'date_histogram',
                  field=field_mapping['create_time'],
                  interval=interval) \
          .metric('avg_response_time', 'avg', field=field_mapping['cost'])

    # 执行查询
    response = s.execute()

    # 创建所有时间点的字典，初始化为0
    time_points = {}

    if time_period == '5m':
        # 每秒1个点
        for i in range(total_points):
            point_time = start_time + timedelta(seconds=i)
            time_key = point_time.strftime(time_format)
            time_points[time_key] = {
                'timestamp': time_key,
                'tps': 0,
                'avg_response_time': 0
            }
    elif time_period == '1h':
        # 每分钟1个点
        for i in range(total_points):
            point_time = start_time + timedelta(minutes=i)
            time_key = point_time.strftime(time_format)
            time_points[time_key] = {
                'timestamp': time_key,
                'tps': 0,
                'avg_response_time': 0
            }
    elif time_period == '1d':
        # 每5分钟1个点，并且是固定的时间点（能被5整除）
        # 先将开始时间调整为能被5整除的分钟数
        start_minutes = (start_time.hour * 60 + start_time.minute) // 5 * 5
        adjusted_start = datetime(start_time.year, start_time.month, start_time.day,
                                  start_minutes // 60, start_minutes % 60, 0)

        for i in range(total_points):
            point_time = adjusted_start + timedelta(minutes=i*5)
            time_key = point_time.strftime(time_format)
            time_points[time_key] = {
                'timestamp': time_key,
                'tps': 0,
                'avg_response_time': 0
            }
    elif time_period == '1w':
        # 每10分钟1个点，并且是固定的时间点（能被10整除）
        # 先将开始时间调整为能被10整除的分钟数
        start_minutes = (start_time.hour * 60 + start_time.minute) // 10 * 10
        adjusted_start = datetime(start_time.year, start_time.month, start_time.day,
                                  start_minutes // 60, start_minutes % 60, 0)

        for i in range(total_points):
            point_time = adjusted_start + timedelta(minutes=i*10)
            time_key = point_time.strftime(time_format)
            time_points[time_key] = {
                'timestamp': time_key,
                'tps': 0,
                'avg_response_time': 0
            }

    # 如果有聚合数据，填充实际值
    if hasattr(response, 'aggregations') and hasattr(response.aggregations, 'time_buckets'):
        for bucket in response.aggregations.time_buckets.buckets:
            # 获取时间点 - 使用key_as_string而不是将key转换为时间
            # 因为时区问题，直接使用key_as_string更可靠
            if hasattr(bucket, 'key_as_string'):
                # 从字符串中提取小时和分钟
                try:
                    dt = datetime.strptime(bucket.key_as_string, '%Y-%m-%d %H:%M:%S')
                    time_key = dt.strftime(time_format)
                except Exception:
                    # 如果解析失败，使用备用方法
                    bucket_time = datetime.fromtimestamp(bucket.key / 1000)  # 毫秒转秒
                    time_key = bucket_time.strftime(time_format)
            else:
                # 如果没有key_as_string，使用key
                bucket_time = datetime.fromtimestamp(bucket.key / 1000)  # 毫秒转秒
                time_key = bucket_time.strftime(time_format)

            # 计算TPS
            # 对于不同的时间间隔，需要进行不同的计算
            # 确保如果有数据，最小值是0.01
            if bucket.doc_count > 0:
                if interval == '1s':
                    # 每秒的请求数就是TPS
                    tps = max(0.01, bucket.doc_count)
                elif interval == '1m':
                    # 每分钟的请求数需要除以60得到每秒的请求数
                    tps = max(0.01, bucket.doc_count / 60)
                elif interval == '5m':
                    # 每5分钟的请求数需要除以300得到每秒的请求数
                    tps = max(0.01, bucket.doc_count / 300)
                elif interval == '10m':
                    # 每10分钟的请求数需要除以600得到每秒的请求数
                    tps = max(0.01, bucket.doc_count / 600)
                else:
                    tps = max(0.01, bucket.doc_count)
            else:
                tps = 0

            # 获取平均响应时间
            avg_response_time = bucket.avg_response_time.value if hasattr(bucket, 'avg_response_time') and bucket.avg_response_time.value is not None else 0

            # 替换字典中的值
            if time_key in time_points:
                time_points[time_key] = {
                    'timestamp': time_key,
                    'tps': round(tps, 2),  # 保留两位小数
                    'avg_response_time': round(avg_response_time, 2)  # 保留两位小数
                }

    # 将字典转换为列表，并按时间戳排序
    time_series = []

    if time_period == '5m':
        # 每秒1个点
        for i in range(total_points):
            point_time = start_time + timedelta(seconds=i)
            time_key = point_time.strftime(time_format)
            if time_key in time_points:
                time_series.append(time_points[time_key])
    elif time_period == '1h':
        # 每分钟1个点
        for i in range(total_points):
            point_time = start_time + timedelta(minutes=i)
            time_key = point_time.strftime(time_format)
            if time_key in time_points:
                time_series.append(time_points[time_key])
    elif time_period == '1d':
        # 每5分钟1个点，并且是固定的时间点（能被5整除）
        start_minutes = (start_time.hour * 60 + start_time.minute) // 5 * 5
        adjusted_start = datetime(start_time.year, start_time.month, start_time.day,
                                  start_minutes // 60, start_minutes % 60, 0)

        for i in range(total_points):
            point_time = adjusted_start + timedelta(minutes=i*5)
            time_key = point_time.strftime(time_format)
            if time_key in time_points:
                time_series.append(time_points[time_key])
    elif time_period == '1w':
        # 每10分钟1个点，并且是固定的时间点（能被10整除）
        start_minutes = (start_time.hour * 60 + start_time.minute) // 10 * 10
        adjusted_start = datetime(start_time.year, start_time.month, start_time.day,
                                  start_minutes // 60, start_minutes % 60, 0)

        for i in range(total_points):
            point_time = adjusted_start + timedelta(minutes=i*10)
            time_key = point_time.strftime(time_format)
            if time_key in time_points:
                time_series.append(time_points[time_key])

    return time_series


def get_operation_logs(start_time=None, end_time=None, page=1, page_size=10, filters=None, system_type='api'):
    """
    获取操作日志明细数据

    :param start_time: 开始时间，默认为当天零点
    :param end_time: 结束时间，默认为当前时间
    :param page: 当前页码，从1开始
    :param page_size: 每页显示的记录数量
    :param filters: 过滤条件，字典类型
    :return: 操作日志明细数据及分页信息
    """
    # 导入需要的模块
    from datetime import datetime

    # 使用当前时间，并考虑到北京时间（UTC+8）
    now = datetime.now()

    # 如果没有指定开始时间，则默认为当天零点
    if start_time is None:
        start_time = datetime(now.year, now.month, now.day, 0, 0, 0)
    elif isinstance(start_time, str):
        try:
            # 尝试解析时间字符串，支持多种格式
            start_time = start_time.replace('+', ' ')  # 处理URL编码的空格
            formats = ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d']  # 支持的时间格式
            for fmt in formats:
                try:
                    start_time = datetime.strptime(start_time, fmt)
                    break
                except ValueError:
                    continue
            else:  # 如果所有格式都不匹配
                print(f"Warning: Could not parse start_time: {start_time}, using default")
                start_time = datetime(now.year, now.month, now.day, 0, 0, 0)
        except Exception as e:
            print(f"Error parsing start_time: {e}")
            start_time = datetime(now.year, now.month, now.day, 0, 0, 0)

    # 如果没有指定结束时间，则默认为当前时间
    if end_time is None:
        end_time = now
    elif isinstance(end_time, str):
        try:
            # 尝试解析时间字符串，支持多种格式
            end_time = end_time.replace('+', ' ')  # 处理URL编码的空格
            formats = ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d']  # 支持的时间格式
            for fmt in formats:
                try:
                    end_time = datetime.strptime(end_time, fmt)
                    break
                except ValueError:
                    continue
            else:  # 如果所有格式都不匹配
                print(f"Warning: Could not parse end_time: {end_time}, using default")
                end_time = now
        except Exception as e:
            print(f"Error parsing end_time: {e}")
            end_time = now

    # 确保开始时间不晚于结束时间
    if start_time > end_time:
        print(f"Warning: start_time {start_time} is later than end_time {end_time}, swapping")
        start_time, end_time = end_time, start_time

    # 如果开始时间和结束时间相同，不再自动增加结束时间
    # 这样可以精确查询到秒级别的数据
    if start_time == end_time:
        print(f"Info: start_time and end_time are the same ({start_time}), querying exact second")
        # 不再增加1秒，而是保持原样

    # 检查时间范围是否合理
    # 如果开始时间过早（比如早于2020年），可能没有数据
    if start_time.year < 2020:
        print(f"Warning: start_time {start_time.year} is before 2020, data may not be available")

    # 格式化日期为Elasticsearch可接受的格式
    start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
    end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')

    # 获取ES索引和字段映射
    log_index = ElasticsearchIndices.get_operation_log_wildcard(system_type)
    field_mapping = ElasticsearchIndices.get_field_mapping(system_type)

    # 构建ES查询
    # 使用lte而不是lt，以确保包含结束时间
    s = Search(using=es_client, index=log_index) \
        .filter('range', **{field_mapping['create_time']: {'gte': start_time_str, 'lte': end_time_str}})

    # 添加过滤条件
    if filters:
        if 'requestPath' in filters and filters['requestPath']:
            s = s.filter('term', **{field_mapping['request_path']: filters['requestPath']})
        if 'accountId' in filters and filters['accountId']:
            # 对于boss系统，使用match_phrase查询operateUserNameAnalyzed字段
            if system_type == 'boss':
                s = s.query('match_phrase', operateUserNameAnalyzed=filters['accountId'])
            else:
                s = s.filter('term', **{field_mapping['user_id']: filters['accountId']})
        if 'ipAddress' in filters and filters['ipAddress']:
            s = s.filter('term', **{field_mapping['ip']: filters['ipAddress']})
        if 'result' in filters and filters['result'] is not None:
            s = s.filter('term', **{field_mapping['result']: filters['result']})
        if 'operateType' in filters and filters['operateType']:
            s = s.filter('term', **{field_mapping['operate_type']: filters['operateType']})

    # 获取总记录数
    total_count = s.count()

    # 分页处理
    from_idx = (page - 1) * page_size

    # 如果超过10000条记录，使用Search After方法
    if from_idx >= 10000:
        print(f"Using Search After for deep pagination: page={page}, from_idx={from_idx}")

        # 按时间降序排序
        s = s.sort(f"-{field_mapping['create_time']}", '_id')

        # 每次获取的数量
        batch_size = 1000

        # 初始查询 - 使用原生ES客户端
        query_dict = s.to_dict()
        query_dict['size'] = batch_size
        query_dict['from'] = 0

        raw_response = es_client.search(index=log_index, body=query_dict)

        # 创建一个简单的响应对象来模拟原始Response
        class SimpleResponse:
            def __init__(self, raw_response):
                self.hits = SimpleHits(raw_response.get('hits', {}))

            def __iter__(self):
                # 返回hits的迭代器
                return iter(self.hits)

        class SimpleHits:
            def __init__(self, hits_dict):
                self.total = hits_dict.get('total', {}).get('value', 0)
                self.hits = [SimpleHit(hit) for hit in hits_dict.get('hits', [])]

            def __len__(self):
                return len(self.hits)

            def __getitem__(self, idx):
                return self.hits[idx]

        class SimpleHit:
            def __init__(self, hit_dict):
                self._source = hit_dict.get('_source', {})
                self.meta = SimpleHitMeta(hit_dict)

                # 将_source的属性直接映射到hit对象
                for key, value in self._source.items():
                    setattr(self, key, value)

        class SimpleHitMeta:
            def __init__(self, hit_dict):
                self.id = hit_dict.get('_id')
                self.index = hit_dict.get('_index')
                self.score = hit_dict.get('_score')
                self.sort = hit_dict.get('sort', [])

        response = SimpleResponse(raw_response)

        # 如果没有结果，直接返回空结果
        if len(response.hits) == 0:
            return {
                'logs': [],
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'total_pages': (total_count + page_size - 1) // page_size
                },
                'time_range': {
                    'start_time': start_time_str,
                    'end_time': end_time_str
                }
            }

        # 获取最后一个结果的排序值
        search_after = [response.hits[-1].meta.sort[0], response.hits[-1].meta.sort[1]]

        # 计算需要跳过的批次
        batches_to_skip = from_idx // batch_size

        # 跳过前面的批次
        for i in range(1, batches_to_skip):
            # 使用原生ES客户端而不是通过params
            query_dict = s.to_dict()
            query_dict['search_after'] = search_after
            query_dict['size'] = batch_size
            query_dict['from'] = 0  # search_after与from不能同时使用

            raw_response = es_client.search(index=log_index, body=query_dict)
            response = SimpleResponse(raw_response)

            # 如果没有更多结果，返回空结果
            if len(response.hits) == 0:
                return {
                    'logs': [],
                    'pagination': {
                        'page': page,
                        'page_size': page_size,
                        'total_count': total_count,
                        'total_pages': (total_count + page_size - 1) // page_size
                    },
                    'time_range': {
                        'start_time': start_time_str,
                        'end_time': end_time_str
                    }
                }

            # 更新search_after值
            search_after = [response.hits[-1].meta.sort[0], response.hits[-1].meta.sort[1]]

        # 处理最后一批的偏移量
        remaining_offset = from_idx % batch_size

        # 获取最后一批数据 - 使用原生ES客户端而不是通过params
        query_dict = s.to_dict()
        query_dict['search_after'] = search_after
        query_dict['size'] = batch_size
        query_dict['from'] = 0  # search_after与from不能同时使用

        raw_response = es_client.search(index=log_index, body=query_dict)
        response = SimpleResponse(raw_response)

        # 如果没有更多结果，返回空结果
        if len(response.hits) == 0:
            return {
                'logs': [],
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'total_pages': (total_count + page_size - 1) // page_size
                },
                'time_range': {
                    'start_time': start_time_str,
                    'end_time': end_time_str
                }
            }

        # 跳过前面的记录
        hits = list(response.hits)
        if remaining_offset > 0:
            hits = hits[remaining_offset:]

        # 只保留page_size条记录
        hits = hits[:page_size]

        # 创建新的响应对象
        response = SimpleResponse(raw_response)
        response.hits.hits = hits
    else:
        # 正常分页处理
        s = s[from_idx:from_idx + page_size]

        # 按时间降序排序
        s = s.sort(f"-{field_mapping['create_time']}")

        # 执行查询
        response = s.execute()

    # 处理结果
    logs = []
    for hit in response:
        # 获取ES的_id字段
        es_id = hit.meta.id if hasattr(hit, 'meta') and hasattr(hit.meta, 'id') else None

        # 获取时间并格式化
        create_time_field = field_mapping['create_time']
        create_time = getattr(hit, create_time_field, '') if hasattr(hit, create_time_field) else ''
        if create_time and isinstance(create_time, str):
            # 如果是字符串并包含T和Z，则进行格式化
            if 'T' in create_time:
                try:
                    # 处理ISO格式的时间字符串
                    from datetime import datetime
                    dt = datetime.fromisoformat(create_time.replace('Z', '+00:00'))
                    create_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                except Exception:
                    # 如果解析失败，保持原样
                    pass

        # 将hit对象转换为字典，以方便访问所有字段
        hit_dict = {}
        for field_name in dir(hit):
            if not field_name.startswith('_') and field_name != 'meta':
                hit_dict[field_name] = getattr(hit, field_name)

        # 使用字段映射获取各个字段的值
        log = {
            'id': es_id,  # 添加id字段
            'createTime': create_time,  # 为了前端显示一致性，使用标准化的字段名
            'cost': getattr(hit, field_mapping['cost'], 0),
            'ipAddress': getattr(hit, field_mapping['ip'], ''),
            'operateType': getattr(hit, field_mapping['operate_type'], ''),
            'accountId': getattr(hit, field_mapping['user_id'], ''),
            'result': getattr(hit, field_mapping['result'], 0),
            'requestPath': getattr(hit, field_mapping['request_path'], ''),
            'className': getattr(hit, field_mapping['class_name'], ''),
            'methodName': getattr(hit, field_mapping['method_name'], ''),
            'methodParam': getattr(hit, field_mapping.get('method_param', 'methodParam'), ''),  # 使用字段映射获取methodParam
            'methodReturn': getattr(hit, 'methodReturn', '')
        }

        # 特殊处理：当system_type为boss时，映射特定字段
        if system_type == 'boss':
            # 将operateUserNameAnalyzed映射为accountId
            if hasattr(hit, 'operateUserNameAnalyzed'):
                log['accountId'] = hit.operateUserNameAnalyzed
            # 将requestNameAnalyzed映射为methodName
            if hasattr(hit, 'requestNameAnalyzed'):
                log['methodName'] = hit.requestNameAnalyzed
            # 将requestParam映射为methodParam
            if hasattr(hit, 'requestParam'):
                log['methodParam'] = hit.requestParam

        logs.append(log)

    # 计算分页信息
    total_pages = (total_count + page_size - 1) // page_size

    return {
        'logs': logs,
        'pagination': {
            'page': page,
            'page_size': page_size,
            'total_count': total_count,
            'total_pages': total_pages
        },
        'time_range': {
            'start_time': start_time_str,
            'end_time': end_time_str
        },
        'system_type': system_type
    }


def get_feature_operation_analysis(date=None, request_url=None, request_type=None, page=1, page_size=10, system_type='api'):
    """
    获取功能操作分析数据

    :param date: 统计日期，默认为当天
    :param request_url: 操作路径过滤条件，支持模糊查询
    :param request_type: 请求类型过滤条件
    :param page: 当前页码，从1开始
    :param page_size: 每页显示的记录数量
    :param system_type: 系统类型，可选值为api(默认)、oss、boss
    :return: 功能操作分析数据
    """
    from django.db.models import Count, Avg, Sum, Q
    from django.db import connection
    from src.apps.analytics.models import UserOperateAnalysisDay
    from datetime import datetime

    # 如果没有指定日期，则默认为当天
    if date is None:
        date = datetime.now().date()
    elif isinstance(date, str):
        try:
            date = datetime.strptime(date, '%Y-%m-%d').date()
        except ValueError:
            # 如果日期格式不正确，则使用当天
            date = datetime.now().date()

    # 查询指定日期和系统类型的数据
    queryset = UserOperateAnalysisDay.objects.filter(day=date, system_type=system_type)

    # 添加过滤条件
    if request_url:
        # 标准化URL，移除末尾的斜杠
        normalized_url = request_url.rstrip('/')
        # 使用OR查询，匹配原始的URL和带斜杠的URL
        queryset = queryset.filter(
            Q(request_url__icontains=normalized_url) |
            Q(request_url__icontains=f"{normalized_url}/")
        )

    if request_type:
        queryset = queryset.filter(request_type=request_type)

    # 如果没有数据，返回空结果
    if not queryset.exists():
        return {
            'data': [],
            'summary': {
                'date': date.strftime('%Y-%m-%d'),
                'total_features': 0,
                'total_requests': 0,
                'avg_time_overall': 0
            }
        }

    # 使用原生SQL查询获取每个功能的操作人数
    # 这是因为Django ORM不直接支持COUNT(DISTINCT)
    with connection.cursor() as cursor:
        # 构建基本查询
        sql = """
            SELECT request_url, COUNT(DISTINCT request_user_id) as user_count
            FROM tgt_api_user_operate_analysis_day
            WHERE day = %s AND system_type = %s AND request_user_id IS NOT NULL AND request_user_id != ''
        """

        # 添加过滤条件
        params = [date, system_type]

        if request_url:
            sql += " AND request_url LIKE %s"
            params.append(f'%{request_url}%')

        if request_type:
            sql += " AND request_type = %s"
            params.append(request_type)

        # 添加分组
        sql += " GROUP BY request_url"

        # 执行查询
        cursor.execute(sql, params)
        user_counts = {row[0]: row[1] for row in cursor.fetchall()}

    # 按功能分组，计算每个功能的总调用次数和平均操作时间
    feature_stats = queryset.values(
        'request_url', 'request_type', 'request_url_name'
    ).annotate(
        avg_time=Avg('avg_time'),
        total_requests=Sum('request_count')
    ).order_by('-total_requests')

    # 处理结果
    all_features = []
    total_requests = 0
    total_time_weighted = 0

    for stat in feature_stats:
        # 获取该功能的操作人数
        user_count = user_counts.get(stat['request_url'], 0)

        # 计算加权总时间（用于计算整体平均时间）
        weighted_time = stat['avg_time'] * stat['total_requests']
        total_time_weighted += weighted_time
        total_requests += stat['total_requests']

        all_features.append({
            'request_url': stat['request_url'],
            'request_type': stat['request_type'],
            'request_url_name': stat['request_url_name'] or stat['request_url'],
            'avg_time': round(stat['avg_time'], 2),
            'total_requests': stat['total_requests'],
            'user_count': user_count,
            'date': date.strftime('%Y-%m-%d')
        })

    # 计算总记录数和总页数
    total_count = len(all_features)
    total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 0

    # 分页处理
    start_idx = (page - 1) * page_size
    end_idx = min(start_idx + page_size, total_count)

    # 获取当前页的数据
    features = all_features[start_idx:end_idx] if start_idx < total_count else []

    # 计算整体平均时间
    avg_time_overall = total_time_weighted / total_requests if total_requests > 0 else 0

    return {
        'data': features,
        'pagination': {
            'page': page,
            'page_size': page_size,
            'total_count': total_count,
            'total_pages': total_pages
        },
        'summary': {
            'date': date.strftime('%Y-%m-%d'),
            'total_features': total_count,
            'total_requests': total_requests,
            'avg_time_overall': round(avg_time_overall, 2),
            'system_type': system_type
        }
    }