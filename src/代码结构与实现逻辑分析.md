# 用户行为分析系统 - API接口与实现分析

## 1. 系统概述

### 1.1 系统架构
用户行为分析系统是一个基于Django的微服务架构系统，主要包含：
- **Django Web应用** - 提供REST API接口和数据分析功能
- **定时任务系统** - 负责定期执行数据聚合和分析任务

### 1.2 技术栈
- **后端框架**: Django 3.2.18 + Django REST Framework
- **数据库**: MySQL 5.7 (统计数据存储) + Elasticsearch 7.x (日志数据查询)
- **缓存**: Redis (数据缓存)
- **任务调度**: APScheduler (定时任务)
- **认证**: JWT (JSON Web Token)

### 1.3 数据源说明
- **Elasticsearch索引**:
  - `tgtweb_apioperatelog`: API系统操作日志
  - `tgtweb_ossoperatelog`: OSS系统操作日志
  - `tgtweb_operatelog`: BOSS系统操作日志
- **MySQL表**:
  - `tgt_api_user_operate_analysis_day`: 用户操作分析日统计表
  - `daily_statistics`: 日统计数据表
  - `weekly_statistics`: 周统计数据表
  - `monthly_statistics`: 月统计数据表

## 2. API接口详细分析

### 2.1 认证接口 (src/apps/users/)

#### 2.1.1 用户登录
- **接口**: `POST /api/analytics/users/auth/login/`
- **实现方式**: Django用户认证 + JWT令牌生成
- **数据源**: MySQL - Django默认User表

**MySQL查询**:
```sql
-- 验证用户凭据
SELECT id, username, password, is_active
FROM auth_user
WHERE username = 'admin' AND is_active = 1;
```

#### 2.1.2 刷新令牌
- **接口**: `POST /api/analytics/users/auth/token/refresh/`
- **实现方式**: JWT令牌刷新机制
- **数据源**: 无需数据库查询，基于JWT令牌验证

#### 2.1.3 获取当前用户信息
- **接口**: `GET /api/analytics/users/auth/me/`
- **实现方式**: 基于JWT令牌获取用户信息
- **数据源**: MySQL - Django默认User表

**MySQL查询**:
```sql
-- 根据JWT中的用户ID获取用户信息
SELECT id, username, email, first_name, last_name, date_joined, last_login
FROM auth_user
WHERE id = {user_id};
```

### 2.2 用户分析接口 (src/apps/users/)

#### 2.2.1 活跃用户统计
- **接口**: `GET /api/analytics/users/analytics/active/`
- **实现方式**: Elasticsearch聚合查询
- **数据源**: Elasticsearch - 操作日志索引

**Elasticsearch DSL**:
```json
# API系统活跃用户统计
GET tgtweb_apioperatelog/_search
{
  "size": 0,
  "query": {
    "range": {
      "createTime": {
        "gte": "2024-01-01 00:00:00",
        "lte": "2024-01-01 23:59:59"
      }
    }
  },
  "aggs": {
    "unique_users": {
      "cardinality": {
        "field": "accountId.keyword"
      }
    }
  }
}

# BOSS系统活跃用户统计
GET tgtweb_operatelog/_search
{
  "size": 0,
  "query": {
    "range": {
      "requestTime": {
        "gte": "2024-01-01 00:00:00",
        "lte": "2024-01-01 23:59:59"
      }
    }
  },
  "aggs": {
    "unique_users": {
      "cardinality": {
        "field": "operateUserId.keyword"
      }
    }
  }
}
```

#### 2.2.2 用户行为分析
- **接口**: `GET /api/analytics/users/analytics/{user_id}/behavior/`
- **实现方式**: Elasticsearch聚合查询用户操作数据
- **数据源**: Elasticsearch - 操作日志索引

**Elasticsearch DSL**:
```json
# 用户行为分析（常用功能和时间分布）
GET tgtweb_apioperatelog/_search
{
  "size": 0,
  "query": {
    "bool": {
      "must": [
        {
          "term": {
            "accountId.keyword": "user123"
          }
        },
        {
          "range": {
            "createTime": {
              "gte": "2024-01-01 00:00:00",
              "lte": "2024-01-07 23:59:59"
            }
          }
        }
      ]
    }
  },
  "aggs": {
    "top_features": {
      "terms": {
        "field": "requestPath.keyword",
        "size": 10
      }
    },
    "time_distribution": {
      "date_histogram": {
        "field": "createTime",
        "interval": "1h"
      }
    }
  }
}
```

#### 2.2.3 用户留存分析
- **接口**: `GET /api/analytics/users/analytics/retention/`
- **实现方式**: Elasticsearch聚合查询 + Python数据处理
- **数据源**: Elasticsearch - 操作日志索引

**Elasticsearch DSL**:
```json
# 用户留存分析
GET tgtweb_apioperatelog/_search
{
  "size": 0,
  "query": {
    "range": {
      "createTime": {
        "gte": "2024-01-01 00:00:00",
        "lte": "2024-01-31 23:59:59"
      }
    }
  },
  "aggs": {
    "users_by_day": {
      "date_histogram": {
        "field": "createTime",
        "interval": "1d"
      },
      "aggs": {
        "unique_users": {
          "terms": {
            "field": "accountId.keyword",
            "size": 10000
          }
        }
      }
    }
  }
}
```

### 2.3 时间维度分析接口 (src/apps/analytics/)

#### 2.3.1 日维度统计
- **接口**: `GET /api/analytics/daily/`
- **实现方式**: 优先MySQL统计表查询，降级到Elasticsearch实时查询
- **数据源**: MySQL - daily_statistics表 / Elasticsearch - 操作日志索引

**MySQL查询**:
```sql
-- 从统计表查询日维度数据
SELECT stat_date, dimension, dimension_value, metric_name, metric_value
FROM daily_statistics
WHERE stat_date >= '2024-01-01'
  AND stat_date <= '2024-01-31'
  AND dimension = 'user'
  AND metric_name = 'count'
ORDER BY stat_date, metric_value DESC
LIMIT 300;
```

**Elasticsearch DSL (降级查询)**:
```json
# 日维度实时统计
GET tgtweb_apioperatelog/_search
{
  "size": 0,
  "query": {
    "range": {
      "createTime": {
        "gte": "2024-01-01 00:00:00",
        "lte": "2024-01-31 23:59:59"
      }
    }
  },
  "aggs": {
    "by_date": {
      "date_histogram": {
        "field": "createTime",
        "interval": "1d"
      },
      "aggs": {
        "by_user": {
          "terms": {
            "field": "accountId.keyword",
            "size": 10
          }
        }
      }
    }
  }
}
```

#### 2.3.2 周维度统计
- **接口**: `GET /api/analytics/weekly/`
- **实现方式**: 优先MySQL统计表查询，降级到Elasticsearch实时查询
- **数据源**: MySQL - weekly_statistics表 / Elasticsearch - 操作日志索引

**MySQL查询**:
```sql
-- 从统计表查询周维度数据
SELECT year_week, start_date, end_date, dimension, dimension_value, metric_name, metric_value
FROM weekly_statistics
WHERE year_week >= '2024-01'
  AND year_week <= '2024-12'
  AND dimension = 'user'
  AND metric_name = 'count'
ORDER BY year_week, metric_value DESC
LIMIT 200;
```

#### 2.3.3 月维度统计
- **接口**: `GET /api/analytics/monthly/`
- **实现方式**: 优先MySQL统计表查询，降级到Elasticsearch实时查询
- **数据源**: MySQL - monthly_statistics表 / Elasticsearch - 操作日志索引

**MySQL查询**:
```sql
-- 从统计表查询月维度数据
SELECT year_month, dimension, dimension_value, metric_name, metric_value
FROM monthly_statistics
WHERE year_month >= '2024-01'
  AND year_month <= '2024-12'
  AND dimension = 'user'
  AND metric_name = 'count'
ORDER BY year_month, metric_value DESC
LIMIT 120;
```

### 2.4 仪表盘接口 (src/apps/analytics/)

#### 2.4.1 系统概览
- **接口**: `GET /api/analytics/dashboard/overview/`
- **实现方式**: Elasticsearch聚合查询多个指标
- **数据源**: Elasticsearch - 操作日志索引

**Elasticsearch DSL**:
```json
# 系统概览数据
GET tgtweb_apioperatelog/_search
{
  "size": 0,
  "query": {
    "range": {
      "createTime": {
        "gte": "now-24h",
        "lte": "now"
      }
    }
  },
  "aggs": {
    "total_requests": {
      "value_count": {
        "field": "requestPath.keyword"
      }
    },
    "unique_users": {
      "cardinality": {
        "field": "accountId.keyword"
      }
    },
    "avg_response_time": {
      "avg": {
        "field": "cost"
      }
    },
    "error_rate": {
      "filter": {
        "term": {
          "result": 1
        }
      }
    }
  }
}
```

#### 2.4.2 活跃用户统计
- **接口**: `GET /api/analytics/dashboard/active-users/`
- **实现方式**: Elasticsearch cardinality聚合
- **数据源**: Elasticsearch - 操作日志索引

**Elasticsearch DSL**:
```json
# 活跃用户统计（支持环比）
GET tgtweb_apioperatelog/_search
{
  "size": 0,
  "query": {
    "range": {
      "createTime": {
        "gte": "now-48h",
        "lte": "now"
      }
    }
  },
  "aggs": {
    "current_period": {
      "filter": {
        "range": {
          "createTime": {
            "gte": "now-24h",
            "lte": "now"
          }
        }
      },
      "aggs": {
        "unique_users": {
          "cardinality": {
            "field": "accountId.keyword"
          }
        }
      }
    },
    "previous_period": {
      "filter": {
        "range": {
          "createTime": {
            "gte": "now-48h",
            "lte": "now-24h"
          }
        }
      },
      "aggs": {
        "unique_users": {
          "cardinality": {
            "field": "accountId.keyword"
          }
        }
      }
    }
  }
}
```

#### 2.4.3 功能调用统计
- **接口**: `GET /api/analytics/dashboard/function-calls/`
- **实现方式**: Elasticsearch value_count聚合
- **数据源**: Elasticsearch - 操作日志索引

**Elasticsearch DSL**:
```json
# 功能调用统计
GET tgtweb_apioperatelog/_search
{
  "size": 0,
  "query": {
    "range": {
      "createTime": {
        "gte": "now-24h",
        "lte": "now"
      }
    }
  },
  "aggs": {
    "total_calls": {
      "value_count": {
        "field": "requestPath.keyword"
      }
    }
  }
}
```

#### 2.4.4 平均响应时间
- **接口**: `GET /api/analytics/dashboard/avg-response-time/`
- **实现方式**: Elasticsearch avg聚合
- **数据源**: Elasticsearch - 操作日志索引

**Elasticsearch DSL**:
```json
# 平均响应时间统计
GET tgtweb_apioperatelog/_search
{
  "size": 0,
  "query": {
    "range": {
      "createTime": {
        "gte": "now-24h",
        "lte": "now"
      }
    }
  },
  "aggs": {
    "avg_response_time": {
      "avg": {
        "field": "cost"
      }
    }
  }
}
```

#### 2.4.5 错误率统计
- **接口**: `GET /api/analytics/dashboard/error-rate/`
- **实现方式**: Elasticsearch filter聚合计算错误率
- **数据源**: Elasticsearch - 操作日志索引

**Elasticsearch DSL**:
```json
# 错误率统计
GET tgtweb_apioperatelog/_search
{
  "size": 0,
  "query": {
    "range": {
      "createTime": {
        "gte": "now-24h",
        "lte": "now"
      }
    }
  },
  "aggs": {
    "total_requests": {
      "value_count": {
        "field": "result"
      }
    },
    "error_requests": {
      "filter": {
        "term": {
          "result": 1
        }
      }
    }
  }
}
```

#### 2.4.6 TPS和响应时间时序数据
- **接口**: `GET /api/analytics/dashboard/tps-and-response-time/`
- **实现方式**: Elasticsearch date_histogram聚合
- **数据源**: Elasticsearch - 操作日志索引

**Elasticsearch DSL**:
```json
# TPS和响应时间时序数据（5分钟粒度）
GET tgtweb_apioperatelog/_search
{
  "size": 0,
  "query": {
    "range": {
      "createTime": {
        "gte": "now-5m",
        "lte": "now"
      }
    }
  },
  "aggs": {
    "time_buckets": {
      "date_histogram": {
        "field": "createTime",
        "interval": "1s"
      },
      "aggs": {
        "avg_response_time": {
          "avg": {
            "field": "cost"
          }
        }
      }
    }
  }
}

# TPS和响应时间时序数据（1小时粒度）
GET tgtweb_apioperatelog/_search
{
  "size": 0,
  "query": {
    "range": {
      "createTime": {
        "gte": "now-1h",
        "lte": "now"
      }
    }
  },
  "aggs": {
    "time_buckets": {
      "date_histogram": {
        "field": "createTime",
        "interval": "1m"
      },
      "aggs": {
        "avg_response_time": {
          "avg": {
            "field": "cost"
          }
        }
      }
    }
  }
}
```

#### 2.4.7 接口TPS和调用记录
- **接口**: `GET /api/analytics/dashboard/interface-tps-and-records/`
- **实现方式**: Elasticsearch terms聚合按接口分组
- **数据源**: Elasticsearch - 操作日志索引

**Elasticsearch DSL**:
```json
# 按接口分组的TPS统计
GET tgtweb_apioperatelog/_search
{
  "size": 0,
  "query": {
    "range": {
      "createTime": {
        "gte": "now-5m",
        "lte": "now"
      }
    }
  },
  "aggs": {
    "by_interface": {
      "terms": {
        "field": "requestPath.keyword",
        "size": 10
      },
      "aggs": {
        "avg_response_time": {
          "avg": {
            "field": "cost"
          }
        },
        "max_response_time": {
          "max": {
            "field": "cost"
          }
        },
        "min_response_time": {
          "min": {
            "field": "cost"
          }
        }
      }
    }
  }
}

# 获取特定接口的最新调用记录
GET tgtweb_apioperatelog/_search
{
  "size": 5,
  "query": {
    "bool": {
      "must": [
        {
          "term": {
            "requestPath.keyword": "/api/user/login"
          }
        },
        {
          "range": {
            "createTime": {
              "gte": "now-24h",
              "lte": "now"
            }
          }
        }
      ]
    }
  },
  "sort": [
    {
      "createTime": {
        "order": "desc"
      }
    }
  ]
}
```

#### 2.4.8 操作日志统计
- **接口**: `GET /api/analytics/dashboard/operation-logs/`
- **实现方式**: Elasticsearch查询最近操作记录
- **数据源**: Elasticsearch - 操作日志索引

**Elasticsearch DSL**:
```json
# 最近操作日志统计
GET tgtweb_apioperatelog/_search
{
  "size": 10,
  "query": {
    "range": {
      "createTime": {
        "gte": "now-1h",
        "lte": "now"
      }
    }
  },
  "sort": [
    {
      "createTime": {
        "order": "desc"
      }
    }
  ],
  "_source": [
    "accountId",
    "requestPath",
    "operateType",
    "result",
    "cost",
    "createTime",
    "ipAddress"
  ]
}
```

#### 2.4.9 用户行为分析
- **接口**: `GET /api/analytics/dashboard/user-behavior-analysis/`
- **实现方式**: Elasticsearch聚合查询用户行为模式
- **数据源**: Elasticsearch - 操作日志索引

#### 2.4.10 功能操作分析
- **接口**: `GET /api/analytics/dashboard/feature-operation-analysis/`
- **实现方式**: Elasticsearch terms聚合分析功能使用情况
- **数据源**: Elasticsearch - 操作日志索引

#### 2.4.11 功能使用统计
- **接口**: `GET /api/analytics/dashboard/feature-usage-stats/`
- **实现方式**: MySQL查询统计表数据
- **数据源**: MySQL - tgt_api_user_operate_analysis_day表

**MySQL查询**:
```sql
-- 功能使用统计
SELECT
    request_url,
    request_url_name,
    SUM(request_count) as total_requests,
    AVG(avg_time) as avg_response_time,
    COUNT(DISTINCT request_user_id) as unique_users,
    system_type
FROM tgt_api_user_operate_analysis_day
WHERE day >= CURDATE() - INTERVAL 7 DAY
  AND system_type = 'api'
GROUP BY request_url, request_url_name, system_type
ORDER BY total_requests DESC
LIMIT 50;
```

### 2.5 日志分析接口 (src/apps/analytics/)

#### 2.5.1 日志记录明细
- **接口**: `GET /api/analytics/log-details/`
- **实现方式**: Elasticsearch多条件过滤查询
- **数据源**: Elasticsearch - 操作日志索引

**Elasticsearch DSL**:
```json
# API系统日志明细查询
GET tgtweb_apioperatelog/_search
{
  "from": 0,
  "size": 10,
  "query": {
    "bool": {
      "must": [
        {
          "range": {
            "createTime": {
              "gte": "2024-01-01 00:00:00",
              "lte": "2024-01-01 23:59:59"
            }
          }
        }
      ],
      "filter": [
        {
          "term": {
            "accountId.keyword": "user123"
          }
        },
        {
          "wildcard": {
            "requestPath.keyword": "*login*"
          }
        },
        {
          "term": {
            "operateType.keyword": "LOGIN"
          }
        },
        {
          "term": {
            "result": 0
          }
        },
        {
          "range": {
            "cost": {
              "gte": 100
            }
          }
        }
      ]
    }
  },
  "sort": [
    {
      "createTime": {
        "order": "desc"
      }
    }
  ]
}

# BOSS系统日志明细查询
GET tgtweb_operatelog/_search
{
  "from": 0,
  "size": 10,
  "query": {
    "bool": {
      "must": [
        {
          "match_phrase": {
            "operateUserNameAnalyzed": "admin"
          }
        },
        {
          "range": {
            "requestTime": {
              "gte": "2024-01-01 00:00:00",
              "lte": "2024-01-01 23:59:59"
            }
          }
        }
      ],
      "filter": [
        {
          "wildcard": {
            "requestUrl.keyword": "*user*"
          }
        },
        {
          "range": {
            "costTime": {
              "gte": 100
            }
          }
        }
      ]
    }
  },
  "sort": [
    {
      "requestTime": {
        "order": "desc"
      }
    }
  ]
}
```

#### 2.5.2 用户访问统计
- **接口**: `GET /api/analytics/access-stats/`
- **实现方式**: Elasticsearch date_histogram按分钟聚合
- **数据源**: Elasticsearch - 操作日志索引

**Elasticsearch DSL**:
```json
# 用户访问统计（按分钟聚合）
GET tgtweb_apioperatelog/_search
{
  "size": 0,
  "query": {
    "bool": {
      "must": [
        {
          "term": {
            "accountId.keyword": "user123"
          }
        },
        {
          "term": {
            "requestPath.keyword": "/api/user/profile"
          }
        },
        {
          "range": {
            "createTime": {
              "gte": "2024-01-01 10:00:00",
              "lte": "2024-01-01 11:00:00"
            }
          }
        }
      ]
    }
  },
  "aggs": {
    "per_minute": {
      "date_histogram": {
        "field": "createTime",
        "interval": "1m",
        "format": "yyyy-MM-dd HH:mm:ss"
      }
    }
  }
}
```

#### 2.5.3 慢查询功能统计
- **接口**: `GET /api/analytics/slow-functions/`
- **实现方式**: Elasticsearch terms聚合按接口分组统计响应时间
- **数据源**: Elasticsearch - 操作日志索引

**Elasticsearch DSL**:
```json
# 慢查询统计（按接口路径聚合）
GET tgtweb_apioperatelog/_search
{
  "size": 0,
  "query": {
    "bool": {
      "must": [
        {
          "range": {
            "createTime": {
              "gte": "2024-01-01 00:00:00",
              "lte": "2024-01-01 23:59:59"
            }
          }
        },
        {
          "range": {
            "cost": {
              "gt": 0
            }
          }
        }
      ]
    }
  },
  "aggs": {
    "by_path": {
      "terms": {
        "field": "requestPath.keyword",
        "size": 20
      },
      "aggs": {
        "avg_cost": {
          "avg": {
            "field": "cost"
          }
        },
        "max_cost": {
          "max": {
            "field": "cost"
          }
        },
        "min_cost": {
          "min": {
            "field": "cost"
          }
        },
        "total_requests": {
          "value_count": {
            "field": "cost"
          }
        }
      }
    }
  }
}
```

### 2.6 功能分析接口 (src/apps/features/)

#### 2.6.1 功能使用情况分析
- **接口**: `GET /api/features/usage/`
- **实现方式**: Elasticsearch terms聚合分析功能使用情况
- **数据源**: Elasticsearch - 操作日志索引

**Elasticsearch DSL**:
```json
# 功能使用情况分析
GET tgtweb_apioperatelog/_search
{
  "size": 0,
  "query": {
    "range": {
      "createTime": {
        "gte": "2024-01-01 00:00:00",
        "lte": "2024-01-01 23:59:59"
      }
    }
  },
  "aggs": {
    "feature_usage": {
      "terms": {
        "field": "requestPath.keyword",
        "size": 50
      },
      "aggs": {
        "unique_users": {
          "cardinality": {
            "field": "accountId.keyword"
          }
        },
        "avg_response_time": {
          "avg": {
            "field": "cost"
          }
        },
        "success_rate": {
          "filter": {
            "term": {
              "result": 0
            }
          }
        }
      }
    }
  }
}
```

#### 2.6.2 功能性能分析
- **接口**: `GET /api/features/performance/`
- **实现方式**: Elasticsearch聚合分析功能性能指标
- **数据源**: Elasticsearch - 操作日志索引

#### 2.6.3 功能使用趋势分析
- **接口**: `GET /api/features/{feature_id}/trend/`
- **实现方式**: Elasticsearch date_histogram时间序列分析
- **数据源**: Elasticsearch - 操作日志索引

#### 2.6.4 功能质量分析
- **接口**: `GET /api/features/quality/`
- **实现方式**: Elasticsearch聚合分析功能质量指标
- **数据源**: Elasticsearch - 操作日志索引

### 2.7 报表接口 (src/apps/reports/)

#### 2.7.1 获取报表模板
- **接口**: `GET /api/reports/templates/`
- **实现方式**: MySQL查询报表配置表
- **数据源**: MySQL - report_config表

**MySQL查询**:
```sql
-- 获取报表模板列表
SELECT
    id,
    report_name,
    report_type,
    config_json,
    created_by,
    created_at,
    is_public
FROM report_config
WHERE is_public = 1
   OR created_by = {user_id}
ORDER BY created_at DESC;
```

#### 2.7.2 生成自定义报表
- **接口**: `POST /api/reports/generate/`
- **实现方式**: 根据配置动态生成Elasticsearch查询
- **数据源**: Elasticsearch - 操作日志索引

#### 2.7.3 导出报表
- **接口**: `POST /api/reports/export/`
- **实现方式**: 查询数据并导出为Excel/CSV格式
- **数据源**: Elasticsearch - 操作日志索引

### 2.8 任务管理接口 (src/apps/tasks/)

#### 2.8.1 获取任务列表
- **接口**: `GET /api/tasks/`
- **实现方式**: MySQL查询任务配置表
- **数据源**: MySQL - scheduled_task表

**MySQL查询**:
```sql
-- 获取任务列表
SELECT
    id,
    task_name,
    task_type,
    cron_expression,
    task_params,
    is_active,
    last_run_time,
    next_run_time,
    created_at,
    updated_at
FROM scheduled_task
WHERE is_active = 1
ORDER BY created_at DESC;
```

#### 2.8.2 获取任务详情
- **接口**: `GET /api/tasks/{id}/`
- **实现方式**: MySQL根据ID查询任务详情
- **数据源**: MySQL - scheduled_task表

**MySQL查询**:
```sql
-- 获取任务详情
SELECT
    id,
    task_name,
    task_type,
    cron_expression,
    task_params,
    is_active,
    last_run_time,
    next_run_time,
    created_at,
    updated_at
FROM scheduled_task
WHERE id = {task_id};
```

#### 2.8.3 创建新任务
- **接口**: `POST /api/tasks/`
- **实现方式**: MySQL插入新任务记录
- **数据源**: MySQL - scheduled_task表

**MySQL查询**:
```sql
-- 创建新任务
INSERT INTO scheduled_task (
    task_name,
    task_type,
    cron_expression,
    task_params,
    is_active,
    created_at,
    updated_at
) VALUES (
    'Daily Analysis Task',
    'user_behavior_analysis',
    '0 1 * * *',
    '{"all_systems": true}',
    1,
    NOW(),
    NOW()
);
```

#### 2.8.4 更新任务配置
- **接口**: `PUT /api/tasks/{id}/`
- **实现方式**: MySQL更新任务配置
- **数据源**: MySQL - scheduled_task表

**MySQL查询**:
```sql
-- 更新任务配置
UPDATE scheduled_task
SET
    task_name = 'Updated Task Name',
    cron_expression = '0 2 * * *',
    task_params = '{"all_systems": true}',
    is_active = 1,
    updated_at = NOW()
WHERE id = {task_id};
```

#### 2.8.5 删除任务
- **接口**: `DELETE /api/tasks/{id}/`
- **实现方式**: MySQL删除任务记录
- **数据源**: MySQL - scheduled_task表

**MySQL查询**:
```sql
-- 删除任务
DELETE FROM scheduled_task
WHERE id = {task_id};
```

#### 2.8.6 获取任务执行日志
- **接口**: `GET /api/tasks/{id}/logs/`
- **实现方式**: MySQL查询任务执行日志表
- **数据源**: MySQL - task_execution_log表

**MySQL查询**:
```sql
-- 获取任务执行日志
SELECT
    id,
    task_id,
    start_time,
    end_time,
    status,
    affected_rows,
    error_message,
    execution_details
FROM task_execution_log
WHERE task_id = {task_id}
ORDER BY start_time DESC
LIMIT 50;
```

## 3. 数据库表结构

### 3.1 MySQL表结构

#### 3.1.1 用户操作分析日统计表
```sql
CREATE TABLE `tgt_api_user_operate_analysis_day` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `system_type` varchar(20) NOT NULL DEFAULT 'api' COMMENT '系统类型',
  `request_url` varchar(256) DEFAULT NULL COMMENT '操作路径url',
  `request_user_id` varchar(64) DEFAULT NULL COMMENT '操作人id',
  `request_url_name` varchar(255) DEFAULT NULL COMMENT '操作路径名称',
  `request_type` varchar(32) NOT NULL COMMENT '业务类型',
  `avg_time` int(10) DEFAULT NULL COMMENT '平均操作时间',
  `request_count` int(10) DEFAULT NULL COMMENT '操作次数',
  `day` date NOT NULL COMMENT '统计时间',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `url_index` (`request_url`(255)),
  KEY `user_id_index` (`request_user_id`),
  KEY `day` (`day`)
);
```

#### 3.1.2 日统计数据表
```sql
CREATE TABLE `daily_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stat_date` date NOT NULL COMMENT '统计日期',
  `dimension` varchar(50) NOT NULL COMMENT '统计维度',
  `dimension_value` varchar(100) NOT NULL COMMENT '维度值',
  `metric_name` varchar(50) NOT NULL COMMENT '指标名称',
  `metric_value` float NOT NULL COMMENT '指标值',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_stat` (`stat_date`,`dimension`,`dimension_value`,`metric_name`)
);
```

#### 3.1.3 任务配置表
```sql
CREATE TABLE `scheduled_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `task_type` varchar(50) NOT NULL COMMENT '任务类型',
  `cron_expression` varchar(100) NOT NULL COMMENT 'Cron表达式',
  `task_params` text COMMENT '任务参数JSON',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `last_run_time` datetime DEFAULT NULL COMMENT '上次运行时间',
  `next_run_time` datetime DEFAULT NULL COMMENT '下次运行时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### 3.2 Elasticsearch索引结构

#### 3.2.1 API系统操作日志索引 (tgtweb_apioperatelog)
- **cost**: 操作消耗时间（毫秒）
- **ipAddress**: 用户IP地址
- **operateType**: 操作类型
- **accountId**: 操作用户ID
- **result**: 请求结果（0成功，1失败）
- **createTime**: 请求时间
- **requestPath**: 请求URL
- **className**: API类名
- **methodName**: API方法名
- **methodParam**: API入参
- **methodReturn**: API返回数据

#### 3.2.2 BOSS系统操作日志索引 (tgtweb_operatelog)
- **costTime**: 操作消耗时间（毫秒）
- **ip**: 用户IP地址
- **operateType**: 操作类型
- **operateUserId**: 操作用户ID
- **operateUserNameAnalyzed**: 操作用户名称
- **operateResult**: 请求结果
- **requestTime**: 请求时间
- **requestUrl**: 请求URL
- **requestNameAnalyzed**: 请求路径名称
- **requestParam**: 入参
- **requestProjectName**: 项目名称
- **recordType**: 记录类型

## 4. 字段映射策略

### 4.1 多系统字段映射
不同系统的字段通过统一映射提供一致的API响应：

#### API系统字段映射
- cost -> cost (响应时间)
- ipAddress -> ipAddress (IP地址)
- operateType -> operateType (操作类型)
- accountId -> accountId (用户ID)
- result -> result (结果)
- createTime -> createTime (创建时间)
- requestPath -> requestPath (请求路径)

#### OSS系统字段映射
- cost -> cost (响应时间)
- ipAddress -> ipAddress (IP地址)
- operateType -> operateType (操作类型)
- operator -> accountId (用户ID映射)
- result -> result (结果)
- createTime -> createTime (创建时间)
- requestPath -> requestPath (请求路径)

#### BOSS系统字段映射
- costTime -> cost (响应时间映射)
- ip -> ipAddress (IP地址映射)
- operateType -> operateType (操作类型)
- operateUserId -> accountId (用户ID映射)
- operateUserNameAnalyzed -> accountId (用户名映射到用户ID)
- operateResult -> result (结果映射)
- requestTime -> createTime (时间字段映射)
- requestUrl -> requestPath (请求路径映射)
- requestNameAnalyzed -> methodName (方法名映射)
- requestParam -> methodParam (参数映射)

## 5. 系统架构特点

### 5.1 数据处理流程
1. **实时查询**: 直接从Elasticsearch查询最新数据
2. **定时聚合**: 每日凌晨1:00执行数据聚合任务
3. **数据存储**: 聚合结果存储到MySQL统计表
4. **API响应**: 优先从统计表查询，降级到ES实时查询

### 5.2 认证与权限
- 使用JWT认证机制
- 访问令牌有效期1小时，刷新令牌7天
- 除登录接口外，所有API都需要认证
- 默认管理员账号：admin/tgt51848

### 5.3 日志管理
- Django服务日志：`logs/django-web.log`, `logs/django-error.log`
- 调度器服务日志：`logs/task/task.log`, `logs/task/task-error.log`
- 按日期和大小轮转，避免日志文件过大

## 6. 总结

### 6.1 系统优势
1. **架构清晰**: 模块化设计，职责分离明确
2. **数据源丰富**: 支持MySQL和Elasticsearch双数据源
3. **多系统兼容**: 统一处理API、OSS、BOSS三个系统的数据
4. **查询策略智能**: 优先统计表查询，降级到ES实时查询
5. **接口完整**: 覆盖用户分析、仪表盘、日志分析等全方位功能

### 6.2 技术特点
1. **认证机制**: JWT认证，支持令牌刷新
2. **数据聚合**: 定时任务进行数据预聚合，提升查询性能
3. **字段映射**: 统一不同系统的字段差异，提供一致的API响应
4. **分页查询**: 支持大数据量的分页查询
5. **多维度分析**: 支持时间、用户、功能等多维度数据分析

### 6.3 需要改进的地方
1. **性能优化**: ES查询可以通过索引优化提升性能
2. **缓存机制**: 增加Redis缓存减少重复查询
3. **异步处理**: 大数据量处理应该异步化
4. **监控告警**: 增加系统监控和告警机制
5. **单元测试**: 提高测试覆盖率
6. **API限流**: 增加接口访问频率限制
7. **数据备份**: 建立自动化数据备份机制

### 6.4 使用建议
1. **开发调试**: 使用提供的DSL语句在Kibana Dev Tools中验证查询
2. **性能监控**: 关注ES查询响应时间，及时优化慢查询
3. **数据一致性**: 定期检查MySQL统计表与ES实时数据的一致性
4. **日志管理**: 定期清理日志文件，避免磁盘空间不足
5. **安全防护**: 定期更新JWT密钥，加强API安全防护

该系统为用户行为分析提供了完整的解决方案，通过合理的架构设计和丰富的API接口，能够满足大部分数据分析需求。建议在实际使用中根据业务需求进行相应的优化和扩展。
