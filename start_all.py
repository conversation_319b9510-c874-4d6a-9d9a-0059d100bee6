#!/usr/bin/env python
# 启动所有服务（Django服务器和简单调度器）

import os
import sys
import time
import subprocess
import signal
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('start_all.log')
    ]
)

logger = logging.getLogger(__name__)

# 进程列表
processes = []

def start_services():
    """启动所有服务"""
    logger.info("开始启动服务...")
    
    # 启动Django服务器
    logger.info("启动Django服务器...")
    django_process = subprocess.Popen([sys.executable, 'run_server.py'])
    processes.append(('Django服务器', django_process))
    logger.info(f"Django服务器已启动，PID: {django_process.pid}")
    
    # 等待Django服务器启动完成
    logger.info("等待Django服务器启动完成...")
    time.sleep(5)
    
    # 启动简单调度器
    logger.info("启动简单调度器...")
    scheduler_process = subprocess.Popen([sys.executable, 'simple_scheduler.py'])
    processes.append(('简单调度器', scheduler_process))
    logger.info(f"简单调度器已启动，PID: {scheduler_process.pid}")
    
    logger.info("所有服务已启动")
    
    # 监控进程状态
    try:
        while True:
            all_running = True
            for name, process in processes:
                if process.poll() is not None:
                    logger.error(f"{name}已停止，退出代码: {process.returncode}")
                    all_running = False
            
            if not all_running:
                logger.error("检测到服务停止，正在关闭所有服务...")
                stop_services()
                break
            
            time.sleep(5)
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务...")
        stop_services()

def stop_services():
    """停止所有服务"""
    logger.info("正在停止所有服务...")
    
    for name, process in processes:
        if process.poll() is None:  # 如果进程仍在运行
            logger.info(f"正在停止{name}...")
            try:
                # 尝试优雅地终止进程
                if os.name == 'nt':  # Windows
                    process.terminate()
                else:  # Unix/Linux
                    process.send_signal(signal.SIGTERM)
                
                # 等待进程终止
                process.wait(timeout=5)
                logger.info(f"{name}已停止")
            except subprocess.TimeoutExpired:
                # 如果进程没有及时终止，强制终止
                logger.warning(f"{name}没有响应，强制终止...")
                process.kill()
                logger.info(f"{name}已强制终止")
    
    logger.info("所有服务已停止")

if __name__ == "__main__":
    start_services()
